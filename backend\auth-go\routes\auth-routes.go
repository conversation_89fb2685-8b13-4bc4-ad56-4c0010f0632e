package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/ton-org/finshark-auth/config" // <-- pour récupérer db
	"github.com/ton-org/finshark-auth/controllers"
)

func SetupRoutes(router *gin.Engine) {
	db := config.GetDb()
	auth := router.Group("/api/auth")
	{
		auth.POST("/register", controllers.RegisterHandler(db))
		auth.POST("/login", controllers.LoginHandler(db))
		auth.GET("/session", controllers.SessionHandler(db))
	}
}
