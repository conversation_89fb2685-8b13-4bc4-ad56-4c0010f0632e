import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();

    const response = await axios.post(
      'http://localhost:8080/api/auth/login',
      body,
      { 
        headers: { 'Content-Type': 'application/json' },
        timeout: 10000
      }
    );

    // Créer la réponse Next.js avec les cookies du backend Go
    const jsonResponse = NextResponse.json(
      { 
        message: response.data.message,
        user: response.data.user 
      }, 
      { status: 200 }
    );
    console.log('response.headers :', response.headers);

    // Transférer les cookies du backend Go
    const setCookieHeader = response.headers['set-cookie'];
    if (setCookieHeader) {
      if (Array.isArray(setCookieHeader)) {
        setCookieHeader.forEach(cookie => {
          jsonResponse.headers.append('Set-Cookie', cookie);
        });
      } else {
        jsonResponse.headers.append('Set-<PERSON><PERSON>', setCookieHeader);
      }
    }

    return jsonResponse;
    
  } catch (err: any) {
    console.error('Login error:', err.response?.data || err.message);
    
    return NextResponse.json(
      err.response?.data || { error: 'Erreur lors de la connexion' },
      { status: err.response?.status || 500 }
    );
  }
}