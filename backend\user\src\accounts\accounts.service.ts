import { Injectable, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateAccountDto } from './dto/create-account.dto';
import { Prisma } from '@prisma/client';

@Injectable()
export class AccountsService {
  constructor(private prisma: PrismaService) {}

  // Vérifier que l'utilisateur existe (appel au backend Go)
  private async validateUserExists(userId: number): Promise<boolean> {
    try {
      // Option 1: Vérification simple en base (si vous avez une table users synchronisée)
      const user = await this.prisma.$queryRaw`SELECT 1 FROM users WHERE id = ${userId} LIMIT 1`;
      return (user as any[]).length > 0;
      
      // Option 2: Appel HTTP au backend Go (à implémenter plus tard)
      // const response = await fetch(`http://localhost:8080/api/users/${userId}`);
      // return response.ok;
    } catch {
      return false;
    }
  }

  async create(userId: number, createAccountDto: CreateAccountDto) {
    // Valider que l'utilisateur existe dans le système Go
    const userExists = await this.validateUserExists(userId);
    if (!userExists) {
      throw new UnauthorizedException('Utilisateur non autorisé');
    }

    // Vérifier les relations existantes
    const [bankExists, accountTypeExists, currencyExists] = await Promise.all([
      this.prisma.bank.findUnique({ where: { id: createAccountDto.bankId } }),
      this.prisma.accountType.findUnique({ where: { id: createAccountDto.accountTypeId } }),
      this.prisma.currency.findUnique({ where: { id: createAccountDto.currencyId } }),
    ]);

    if (!bankExists) throw new NotFoundException('Banque non trouvée');
    if (!accountTypeExists) throw new NotFoundException('Type de compte non trouvé');
    if (!currencyExists) throw new NotFoundException('Devise non trouvée');

    const account = await this.prisma.account.create({
      data: {
        name: createAccountDto.name,
        accountNumber: createAccountDto.accountNumber,
        bankId: createAccountDto.bankId,
        accountTypeId: createAccountDto.accountTypeId,
        currencyId: createAccountDto.currencyId,
        balance: new Prisma.Decimal(createAccountDto.balance),
        userId: userId, // ID de l'utilisateur Go
      },
      include: {
        bank: true,
        accountType: true,
        currency: true,
      },
    });

    return account;
  }

  async findAllByUser(userId: number) {
    const userExists = await this.validateUserExists(userId);
    if (!userExists) {
      throw new UnauthorizedException('Utilisateur non autorisé');
    }

    return this.prisma.account.findMany({
      where: { userId: userId },
      include: {
        bank: true,
        accountType: true,
        currency: true,
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  async findOne(id: number, userId: number) {
    const userExists = await this.validateUserExists(userId);
    if (!userExists) {
      throw new UnauthorizedException('Utilisateur non autorisé');
    }

    const account = await this.prisma.account.findUnique({
      where: { id: id, userId: userId },
      include: {
        bank: true,
        accountType: true,
        currency: true,
      },
    });

    if (!account) {
      throw new NotFoundException('Compte non trouvé');
    }

    return account;
  }

  // Méthode remove manquante
  async remove(id: number, userId: number) {
    const userExists = await this.validateUserExists(userId);
    if (!userExists) {
      throw new UnauthorizedException('Utilisateur non autorisé');
    }

    const account = await this.findOne(id, userId);
    
    return this.prisma.account.delete({
      where: {
        id: id,
        userId: userId,
      },
    });
  }

  // Méthodes utilitaires pour les données de référence
  async getBanks() {
    return this.prisma.bank.findMany({ orderBy: { name: 'asc' } });
  }

  async getAccountTypes() {
    return this.prisma.accountType.findMany({ orderBy: { name: 'asc' } });
  }

  async getCurrencies() {
    return this.prisma.currency.findMany({ orderBy: { code: 'asc' } });
  }
}