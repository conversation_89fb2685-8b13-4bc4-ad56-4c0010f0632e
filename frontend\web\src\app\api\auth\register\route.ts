import axios from 'axios';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  const body = await req.json();
  console.log("payload recu du formulaire", body);

  try {
    const response = await axios.post(
      'http://localhost:8080/api/auth/register', // endpoint NestJS
      body,
      { headers: { 'Content-Type': 'application/json' } }
    );
    console.log('response :', response.data);
    return NextResponse.json(response.data, { status: 201 });
  } catch (err: any) {
    console.error('err :' , err.response?.data || err.message );
    return NextResponse.json(
      err.response?.data || { message: 'Erreur serveur' },
      { status: err.response?.status || 500 }
    );
  }
}