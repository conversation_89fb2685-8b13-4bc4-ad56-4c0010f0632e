import { Pie<PERSON><PERSON> } from "lucide-react";

export default function CategoryDepense() {
  return (
    <div className="bg-slate-800/40 backdrop-blur-sm rounded-xl border border-slate-700/50 p-4">
    <div className="flex items-center gap-2 mb-3">
      <PieChart className="w-4 h-4 text-purple-400" />
      <span className="text-xs font-inter text-slate-400">CATÉGORIES</span>
    </div>
    <div className="space-y-2">
      {[
        { name: 'Alimentation', percent: 25, color: 'bg-emerald-500' },
        { name: 'Transport', percent: 18, color: 'bg-blue-500' },
        { name: 'Lois<PERSON>', percent: 15, color: 'bg-purple-500' },
        { name: 'Logement', percent: 42, color: 'bg-amber-500' },
      ].map((item, index) => (
        <div key={index} className="flex items-center">
          <div className="w-3 h-3 rounded-full mr-2"></div>
          <div className="text-xs font-inter text-slate-300 w-16 truncate">{item.name}</div>
          <div className="flex-1 ml-2">
            <div className="h-1.5 rounded-full bg-slate-700">
              <div 
                className={`h-1.5 rounded-full ${item.color}`} 
                style={{ width: `${item.percent}%` }}
              ></div>
            </div>
          </div>
          <div className="text-xs font-inter text-slate-400 ml-2">{item.percent}%</div>
        </div>
      ))}
    </div>
  </div>
  );
}