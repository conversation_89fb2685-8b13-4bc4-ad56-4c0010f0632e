// frontend/web/src/app/api/auth/session/route.ts
import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

export async function GET(request: NextRequest) {
  // Récupère le cookie du navigateur
  const token = request.cookies.get('token')?.value;

  if (!token) {
    return NextResponse.json({ user: null }, { status: 401 });
  }

  try {
    const baseUrlGo = process.env.API_URL_GO;
    // Appel à Go pour vérifier la session
    const response = await axios.get(`${baseUrlGo}/api/auth/session`, {
      headers: {
        'Cookie': `token=${token}`,
      },
      // Optionnel : désactive le proxy si tu en as un en dev
      proxy: false,
    });

    const user = response.data; // ✅ Pas de `await` ici

    return NextResponse.json({ user });
  } catch (error: any) {
    console.error('Erreur vérification session:', error.message);
    
    // Gère les erreurs axios
    if (error.response?.status === 401) {
      return NextResponse.json({ user: null }, { status: 401 });
    }

    return NextResponse.json({ user: null }, { status: 500 });
  }
}