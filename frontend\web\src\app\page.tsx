import FeaturesSection from '@/components/pages/home/<USER>';
import Header from '@/components/pages/home/<USER>';
import HeroSection from '@/components/pages/home/<USER>/HeroSection';
import SecuritySection from '@/components/pages/home/<USER>';
import Image from "next/image";

export default function Home() {
  return (
    <div className="min-h-screen  text-text overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 left-0 w-96 h-96 bg-emerald-500/10 rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute bottom-0 right-0 w-80 h-80 bg-blue-500/10 rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute top-1/3 right-1/4 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl animate-pulse-slow"></div>
        
        {/* Geometric shapes */}
        <div className="absolute top-20 left-1/4 w-48 h-48 border-2 border-emerald-400/30 rounded-lg rotate-45"></div>
        <div className="absolute bottom-40 right-1/3 w-32 h-32 border-2 border-blue-400/30 rounded-full"></div>
        <div className="absolute top-1/2 left-20 w-24 h-24 border-2 border-purple-400/30 rotate-12"></div>
      </div>

      {/* Header */}
      <Header />
      {/* Hero Section */}
      <HeroSection />

      {/* Features Section */}
     <FeaturesSection />

      {/* Security Section */}
      <SecuritySection />

    </div>
  );
}

// Custom animations in Tailwind config


