import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Chart, Wallet, CreditCard, TrendingUp, DollarSign, ArrowDown, ArrowUp, List<PERSON><PERSON>red, Goal } from 'lucide-react';
import SoldeTotal from './components/elements/SoldeTotal';
import DepenseMouth from './components/elements/DepenseMouth';
import Evolution from './components/elements/Evolution';
import CategoryDepense from './components/elements/CategoryDepense';
import Objectifs from './components/elements/Objectifs';
import Transactions from './components/elements/transactions';

export default function HeroSection() {
  return (
    <section className="relative z-10 max-w-7xl mx-auto px-6 py-20 md:py-32 flex flex-col items-center text-center">
      <div className="mb-6 px-4 py-1.5 bg-slate-800/50 backdrop-blur-sm rounded-full border border-slate-700 inline-flex items-center gap-2">
        <Sparkles className="w-4 h-4 text-yellow-400" />
        <span className="font-inter text-sm text-slate-300">Révolutionnez votre gestion financière</span>
      </div>
      
      <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold font-satoshi max-w-4xl leading-tight md:leading-snug">
        <span className="bg-gradient-to-r from-blue-300 to-emerald-300 bg-clip-text text-transparent">
          Prenez le contrôle
        </span>{' '}
        de vos finances avec une intelligence artificielle dédiée
      </h1>
      
      <p className="mt-8 max-w-2xl text-lg md:text-xl text-slate-400 font-inter leading-relaxed">
        FinShark combine sécurité bancaire de niveau militaire et IA prédictive pour transformer votre relation avec l'argent. 
        Découvrez une gestion financière intuitive et proactive.
      </p>
      
      <div className="mt-12 flex flex-col sm:flex-row gap-4">
        <button className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl font-medium font-inter text-white text-lg flex items-center gap-2 hover:shadow-lg hover:shadow-blue-500/30 transition-all duration-300 transform hover:scale-[1.03]">
          Commencer maintenant <ArrowRight className="w-5 h-5" />
        </button>
        <button className="px-8 py-4 bg-slate-800/50 backdrop-blur-sm rounded-xl font-medium font-inter text-slate-300 border border-slate-700 text-lg hover:bg-slate-800 transition-colors">
          Voir la démo
        </button>
      </div>
      
      <div className="mt-16 w-full max-w-4xl aspect-video bg-gradient-to-br from-slate-800/30 to-slate-900/50 backdrop-blur-lg border border-slate-700/50 rounded-3xl shadow-2xl overflow-hidden relative">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="relative w-3/4 h-3/4 bg-slate-800/20 border border-slate-700/50 rounded-2xl overflow-hidden p-4 grid grid-cols-3 grid-rows-2 gap-4">
            {/* Élément 1: Solde total */}
            <SoldeTotal />
            
            {/* Élément 2: Dépenses mensuelles */}
           <DepenseMouth />
            
            {/* Élément 3: Graphique en courbes */}
            <Evolution />
            
            {/* Élément 4: Catégories de dépenses */}
           <CategoryDepense />
            
            {/* Élément 5: Objectifs d'épargne */}
            <Objectifs />
            
            {/* Élément 6: Dernières transactions */}
            <Transactions />
          </div>
        </div>
        
        {/* Animated elements */}
        <div className="absolute top-1/4 left-1/4 w-8 h-8 bg-emerald-400/10 rounded-full animate-ping"></div>
        <div className="absolute bottom-1/3 right-1/3 w-6 h-6 bg-blue-400/10 rounded-full animate-ping animation-delay-1000"></div>
      </div>
    </section>
  );
}