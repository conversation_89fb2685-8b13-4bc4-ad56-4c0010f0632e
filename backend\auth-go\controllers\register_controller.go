package controllers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/ton-org/finshark-auth/models"
	"github.com/ton-org/finshark-auth/services"
	"github.com/ton-org/finshark-auth/utils"
	"gorm.io/gorm"
)

func RegisterHandler(db *gorm.DB) gin.HandlerFunc {
	validate := utils.GetValidator()
	
	return func(c *gin.Context) {
		var req models.RegisterRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Données invalides"})
			return
		}

		// Validation avec validator/v10
		if err := validate.Struct(req); err != nil {
			var errors []map[string]string
			
			for _, err := range err.(validator.ValidationErrors) {
				errors = append(errors, map[string]string{
					"field":   err.<PERSON>(),
					"message": formatValidationErrorRegister(err),
				})
			}
			
			c.<PERSON>(http.StatusBadRequest, gin.H{"errors": errors})
			return
		}

		user := models.User{
			Username: req.Username,
			Email:    req.Email,
			Password: req.Password,
		}

		token, err := services.RegisterUser(db, &user, req.ConfirmPassword, req.AcceptTerms)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"message": "Compte créé avec succès",
			"token":   token,
		})
	}
}

func formatValidationErrorRegister(err validator.FieldError) string {
	switch err.Tag() {
	case "required":
		return "Ce champ est requis"
	case "email":
		return "Le format de l'email est invalide"
	case "min":
		return "Ce champ est trop court"
	case "max":
		return "Ce champ est trop long"
	case "alphanum":
		return "Ce champ ne doit contenir que des caractères alphanumériques"
	case "eqfield":
		return "Les mots de passe ne correspondent pas"
	default:
		return "Validation échouée"
	}
}