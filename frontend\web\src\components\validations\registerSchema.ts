import joi from 'joi';

const registerSchema = joi.object({
  username: joi.string().min(3).max(20).required().messages({
    'any.required': 'Le nom d\'utilisateur est requis',
    'string.min': 'Le nom d\'utilisateur doit contenir au moins 3 caractères',
    'string.max': 'Le nom d\'utilisateur ne peut pas contenir plus de 20 caractères',
  }),
  email: joi.string().email({ tlds: { allow: false } }).required().messages({
    'any.required': 'L\'adresse email est requise',
    'string.email': 'L\'adresse email doit être valide',
  }),
  password: joi.string().min(8).required().messages({
    'any.required': 'Le mot de passe est requis',
    'string.min': 'Le mot de passe doit contenir au moins 8 caractères',
    'string.pattern.base': 'Le mot de passe doit contenir au moins 1 majuscule, 1 minuscule, un chiffre et un caractère spécial',
  }),
  confirmPassword: joi.string().valid(joi.ref('password')).required().messages({
    'any.required': 'La confirmation du mot de passe est requise',
    'any.only': 'La confirmation du mot de passe doit être identique au mot de passe',
  }),
  acceptTerms: joi.boolean().valid(true).required().messages({
    'any.required': 'Vous devez accepter les termes et conditions',
  })
});

export default registerSchema;
