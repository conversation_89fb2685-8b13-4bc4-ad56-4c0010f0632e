'use client';
import { useState } from 'react';
import axios from 'axios';
import { motion } from 'framer-motion';
import Image from 'next/image';
import Input from '../../ui/Input';
import Button from '../../ui/Button';
import Checkbox from '../../ui/Checkbox';
import { BsEnvelope, BsPerson, BsPersonAdd } from 'react-icons/bs';
import { TbLock } from 'react-icons/tb';
import registerSchema from '@/components/validations/registerSchema';
import { toastError, toastSuccess } from '@/components/lib/toast';
import { useRouter } from 'next/navigation';

export default function FormRegister() {
  const router = useRouter();
  const [errorsValidation, setErrorsValidation] = useState<{
    [key: string]: string;
  }>({});
  const [formState, setFormState] = useState({
    username: "",
    email: "",
    password: "",
    confirmPassword: "",
    acceptTerms: false,
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, type, value, checked } = e.target;
    setFormState((formState) => ({
      ...formState,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    const { error: errorValidate } = registerSchema.validate(formState, {
      abortEarly: false,
    });
    if (errorValidate) {
      // Créer un objet d'erreur pour chaque champ
      const fieldErrors: Record<string, string> = {};
      errorValidate.details.forEach((error: any) => {
        fieldErrors[error.path[0]] = error.message;
      });
      setErrorsValidation(fieldErrors);
      toastError(errorValidate.details[0].message);
      return;
    }

    try {
      const { data } = await axios.post("/api/register", formState);
      toastSuccess(data.message);
      setTimeout(() => {
        router.push("/");
      }, 2000);
    } catch (err: any) {
      toastError(err.response?.data.error || "Erreur serveur");
    }
  };
  return (
    <motion.div
      className="flex items-center justify-center p-4"
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        className="w-full max-w-md bg-white dark:bg-slate-800 rounded-2xl shadow-2xl border border-slate-200 dark:border-slate-700 p-8"
        whileHover={{ y: -4 }}
        transition={{ type: "spring", stiffness: 300 }}
      >

        <motion.div
          className="space-y-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <div className="text-center lg:text-left">
            <h2 className="text-3xl font-bold font-satoshi text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400">
              Créer votre compte
            </h2>
            <p className="mt-2 text-slate-600 dark:text-slate-400 font-inter">
              Commencez à gérer votre argent simplement et en toute
              confidentialité.
            </p>
          </div>

          <form className="space-y-6" onSubmit={handleSubmit}>
            {/* Champ username */}
            <div className="group">
              <Input
                label="Nom d'utilisateur"
                name="username"
                type="text"
                value={formState.username}
                onChange={handleInputChange}
                placeholder="Votre nom d'utilisateur"
                error={errorsValidation.username}
                icon={({ className }) => (
                  <BsPerson className={`text-primary `} />
                )}
                className="border-slate-300 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400 group-hover:shadow-lg transition-shadow"
              />
            </div>

            {/* Champ email */}
            <div className="group">
              <Input
                label="Adresse email"
                name="email"
                type="email"
                value={formState.email}
                onChange={handleInputChange}
                error={errorsValidation.email}
                placeholder="<EMAIL>"
                icon={({ className }) => (
                  <BsEnvelope className={`text-primary `} />
                )}
                className="border-slate-300 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400 group-hover:shadow-lg transition-shadow"
              />
            </div>

            {/* Champ password */}
            <div className="group">
              <Input
                label="Mot de passe"
                name="password"
                type="password"
                value={formState.password}
                onChange={handleInputChange}
                error={errorsValidation.password}
                placeholder="Minimum 8 caractères"
                icon={({ className }) => <TbLock className={`text-primary `} />}
                className="border-slate-300 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400 group-hover:shadow-lg transition-shadow"
              />
            </div>

            {/* Champ confirm password */}
            <div className="group">
              <Input
                label="Confirmer le mot de passe"
                name="confirmPassword"
                type="password"
                value={formState.confirmPassword}
                onChange={handleInputChange}
                error={errorsValidation.confirmPassword}
                placeholder="Confirmez votre mot de passe"
                icon={({ className }) => <TbLock className={`text-primary `} />}
                className="border-slate-300 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400 group-hover:shadow-lg transition-shadow"
              />
            </div>

            {/* Checkbox RGPD */}
            <motion.div
              className="p-4 rounded-lg border border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-800/50"
              whileHover={{ scale: 1.01 }}
            >
              <Checkbox
                name="acceptTerms"
                checked={formState.acceptTerms}
                onChange={handleInputChange}
                label={
                  <span className="text-sm">
                    J'accepte les{" "}
                    <a
                      href="#"
                      className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium underline"
                    >
                      conditions d'utilisation
                    </a>{" "}
                    et la{" "}
                    <a
                      href="#"
                      className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium underline"
                    >
                      politique de confidentialité
                    </a>{" "}
                    de FinShark. Mes données restent locales et ne seront jamais
                    partagées.
                  </span>
                }
              />
            </motion.div>

            {/* Bouton lumineux */}
            <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
              <Button
                type="submit"
                className="w-full py-3 rounded-xl bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-4 focus:ring-blue-500/50 dark:focus:ring-blue-400/50 transition-all duration-300 shadow-lg hover:shadow-xl"
                size="lg"
                icon={BsPersonAdd}
              >
                Créer mon compte FinShark
              </Button>
            </motion.div>
          </form>

          {/* Lien connexion */}
          <motion.div className="text-center" whileHover={{ scale: 1.05 }}>
            <p className="text-slate-600 dark:text-slate-400 font-inter">
              Déjà un compte ?{" "}
              <a
                href="#"
                className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium underline"
              >
                Accéder à mon tableau de bord
              </a>
            </p>
          </motion.div>
        </motion.div>
      </motion.div>
    </motion.div>
  );
}