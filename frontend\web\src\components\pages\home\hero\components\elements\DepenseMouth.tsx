import { CreditCard, ArrowDown } from "lucide-react";

export default function DepenseMouth() {
  return (
    <div className="bg-slate-800/40 backdrop-blur-sm rounded-xl border border-slate-700/50 p-4 flex flex-col items-start">
    <div className="flex items-center gap-2 mb-3">
      <CreditCard className="w-4 h-4 text-orange-400" />
      <span className="text-xs font-inter text-slate-400">DÉPENSES MOIS</span>
    </div>
    <div className="text-xl font-bold font-satoshi text-white">2,340.20 €</div>
    <div className="w-full bg-slate-700 rounded-full h-1.5 mt-3">
      <div className="bg-orange-400 h-1.5 rounded-full" style={{ width: '65%' }}></div>
    </div>
    <div className="flex items-center gap-1 mt-2">
      <ArrowDown className="w-3 h-3 text-orange-400" />
      <span className="text-xs text-orange-400 font-inter">-8.2%</span>
    </div>
  </div>
  );
}