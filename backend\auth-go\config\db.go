package config

import (
	"github.com/ton-org/finshark-auth/models"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var DB *gorm.DB

func InitDB() {
	dsn := "host=localhost user=postgres password=140479 dbname=finshark port=5432 sslmode=disable"
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	if err != nil {
		panic("❌ DB connection failed : " + err.Error())
	}

	db.AutoMigrate(&models.User{})
	DB = db
}

func GetDb() *gorm.DB {
	return DB
}
