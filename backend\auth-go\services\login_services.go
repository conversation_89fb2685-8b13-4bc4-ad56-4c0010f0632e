package services

import (
	"fmt"
	"github.com/ton-org/finshark-auth/models"
	"github.com/ton-org/finshark-auth/utils"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

func LoginUser(db *gorm.DB, email, password string) (string, *models.User, error) {
	// Validation des inputs
	if email == "" || password == "" {
		return "", nil, fmt.Errorf("email et mot de passe requis")
	}

	// Validation de l'email avec le validator
	validate := utils.GetValidator()
	loginReq := models.LoginRequest{Email: email, Password: password}
	if err := validate.Struct(loginReq); err != nil {
		return "", nil, fmt.Errorf("format d'email invalide")
	}

	// Rechercher l'utilisateur
	var user models.User
	if err := db.Where("email = ?", email).First(&user).Error; err != nil {
		// Ne pas révéler si l'email existe ou non (sécurité)
		return "", nil, fmt.Errorf("identifiants invalides")
	}

	// Vérifier le mot de passe
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password)); err != nil {
		return "", nil, fmt.Errorf("identifiants invalides")
	}

	// Générer le JWT
	token, err := utils.GenerateJWT(user.ID, user.Email)
	if err != nil {
		return "", nil, fmt.Errorf("erreur lors de la génération du token")
	}

	// Ne pas renvoyer le mot de passe hashé
	user.Password = ""

	return token, &user, nil
}