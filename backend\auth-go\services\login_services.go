package services

import (
	"fmt"

	"github.com/ton-org/finshark-auth/models"
	"github.com/ton-org/finshark-auth/utils"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

func LoginUser(db *gorm.DB, email, password string) (string, *models.User, error) {
	// Validation des inputs
	if email == "" || password == "" {
		return "", nil, fmt.Errorf("email et mot de passe requis")
	}

	// Validation de l'email avec le validator
	validate := utils.GetValidator()
	loginReq := models.LoginRequest{Email: email, Password: password}
	if err := validate.Struct(loginReq); err != nil {
		return "", nil, fmt.Errorf("format d'email invalide")
	}

	// Debug: Log de la tentative de connexion
	fmt.Printf("DEBUG: Tentative de connexion pour l'email: %s\n", email)

	// Rechercher l'utilisateur
	var user models.User
	if err := db.Where("email = ?", email).First(&user).Error; err != nil {
		// Debug: Log si l'utilisateur n'est pas trouvé
		fmt.Printf("DEBUG: Utilisateur non trouvé pour l'email: %s, erreur: %v\n", email, err)
		return "", nil, fmt.Errorf("identifiants invalides")
	}

	// Debug: Log si l'utilisateur est trouvé
	fmt.Printf("DEBUG: Utilisateur trouvé - ID: %d, Email: %s, Username: %s\n", user.ID, user.Email, user.Username)

	// Vérifier le mot de passe
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password)); err != nil {
		// Debug: Log si le mot de passe ne correspond pas
		fmt.Printf("DEBUG: Mot de passe incorrect pour l'email: %s\n", email)
		return "", nil, fmt.Errorf("identifiants invalides")
	}

	// Debug: Log si la connexion réussit
	fmt.Printf("DEBUG: Connexion réussie pour l'email: %s\n", email)

	// Générer le JWT
	token, err := utils.GenerateJWT(user.ID, user.Email)
	if err != nil {
		return "", nil, fmt.Errorf("erreur lors de la génération du token")
	}

	// Ne pas renvoyer le mot de passe hashé
	user.Password = ""

	return token, &user, nil
}
