import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

const BACKEND_URL = 'http://localhost:3001';

// Fonction utilitaire pour forward les requêtes
async function forwardRequest(req: NextRequest, backendPath: string) {
  try {
    const url = new URL(req.url);
    const backendUrl = `${BACKEND_URL}${backendPath}${url.search}`;

    // Récupérer le body si c'est une requête POST/PUT/PATCH
    let body = null;
    if (['POST', 'PUT', 'PATCH'].includes(req.method || '')) {
      try {
        body = await req.json();
      } catch {
        body = null;
      }
    }

    const response = await axios({
      method: req.method,
      url: backendUrl,
      data: body,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': req.headers.get('authorization') || '',
      },
      timeout: 10000,
    });

    return NextResponse.json(response.data, { status: response.status });
    
  } catch (err: any) {
    console.error(`API Error ${req.method} ${backendPath}:`, err.response?.data || err.message);
    
    return NextResponse.json(
      err.response?.data || { error: 'Erreur lors de la requête' },
      { status: err.response?.status || 500 }
    );
  }
}

// GET - Récupérer les comptes ou données de référence
export async function GET(req: NextRequest, { params }: { params: Promise<{ slug: string[] }> }) {
  // Await les params comme requis par Next.js 15
  const awaitedParams = await params;
  const slug = awaitedParams.slug || [];
  const path = slug.length > 0 ? `/${slug.join('/')}` : '';
  
  return forwardRequest(req, `/accounts${path}`);
}

// POST - Créer un compte
export async function POST(req: NextRequest, { params }: { params: Promise<{ slug: string[] }> }) {
  // Await les params comme requis par Next.js 15
  const awaitedParams = await params;
  const slug = awaitedParams.slug || [];
  const path = slug.length > 0 ? `/${slug.join('/')}` : '';
  
  return forwardRequest(req, `/accounts${path}`);
}

// DELETE - Supprimer un compte
export async function DELETE(req: NextRequest, { params }: { params: Promise<{ slug: string[] }> }) {
  // Await les params comme requis par Next.js 15
  const awaitedParams = await params;
  const slug = awaitedParams.slug || [];
  const path = slug.length > 0 ? `/${slug.join('/')}` : '';
  
  return forwardRequest(req, `/accounts${path}`);
}