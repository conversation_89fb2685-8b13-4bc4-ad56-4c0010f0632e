// controllers/session_controller.go

package controllers

import (
	"net/http"
	"os"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/ton-org/finshark-auth/models"
	"gorm.io/gorm"
)

func SessionHandler(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		token, err := c.<PERSON>("token")
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Non connecté"})
			return
		}

		claims := jwt.MapClaims{}
		jwtSecret := os.Getenv("JWT_SECRET_AUTH")
		if jwtSecret == "" {
			// Utilise la même clé par défaut que dans utils/jwt.go
			jwtSecret = "super_secret_key_for_development"
		}

		_, err = jwt.ParseWithClaims(token, claims, func(token *jwt.Token) (interface{}, error) {
			return []byte(jwtSecret), nil
		})

		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Session invalide"})
			return
		}

		userID := uint(claims["user_id"].(float64))
		var user models.User
		if err := db.First(&user, userID).Error; err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Utilisateur introuvable"})
			return
		}

		// Ne pas exposer le mot de passe
		user.Password = ""
		c.JSON(http.StatusOK, user)
	}
}
