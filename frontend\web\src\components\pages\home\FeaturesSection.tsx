import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ard, <PERSON><PERSON><PERSON>, ArrowRight } from "lucide-react";


const features = [
  {
    icon: <BarChart className="w-6 h-6" />,
    title: "Analyse Prédictive",
    description: "Notre IA anticipe vos tendances de dépenses et propose des ajustements en temps réel."
  },
  {
    icon: <CreditCard className="w-6 h-6" />,
    title: "Portefeuille Digital",
    description: "Gérez tous vos comptes et investissements depuis une interface unifiée et sécurisée."
  },
  {
    icon: <Sparkles className="w-6 h-6" />,
    title: "Assistant Financier",
    description: "Un coach virtuel disponible 24/7 pour optimiser vos finances et atteindre vos objectifs."
  }
];


export default function FeaturesSection() {
  return (
    <section className="relative z-10 max-w-7xl mx-auto px-6 py-20">
    <div className="text-center mb-20">
      <h2 className="text-3xl md:text-4xl font-bold font-satoshi mb-6">
        Une <span className="bg-gradient-to-r from-blue-400 to-emerald-400 bg-clip-text text-transparent">expérience unique</span> en gestion financière
      </h2>
      <p className="max-w-2xl mx-auto text-slate-400 font-inter">
        FinShark réinvente la banque personnelle avec des fonctionnalités exclusives conçues pour votre liberté financière.
      </p>
    </div>
    
    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
      {features.map((feature, index) => (
        <div 
          key={index}
          className="bg-slate-800/30 backdrop-blur-sm rounded-2xl p-6 border border-slate-700/50 hover:border-slate-600 transition-all duration-300 hover:shadow-xl hover:shadow-blue-500/10"
        >
          <div className="w-14 h-14 rounded-xl bg-gradient-to-br from-blue-600/20 to-purple-600/20 flex items-center justify-center mb-6">
            <div className="text-blue-400">
              {feature.icon}
            </div>
          </div>
          <h3 className="text-xl font-bold font-satoshi mb-3">{feature.title}</h3>
          <p className="text-slate-400 font-inter mb-4">{feature.description}</p>
          <div className="mt-4">
            <button className="flex items-center gap-2 text-slate-300 hover:text-white font-inter group">
              En savoir plus
              <ArrowRight className="w-4 h-4 transition-transform group-hover:translate-x-1" />
            </button>
          </div>
        </div>
      ))}
    </div>
  </section>
  )
}