package models

import "time"

type User struct {
	ID         uint      `gorm:"primaryKey" json:"id"`
	Username   string    `gorm:"unique;not null" json:"username" validate:"required,min=3,max=30,alphanum"`
	Email      string    `gorm:"unique;not null" json:"email" validate:"required,email"`
	Password   string    `gorm:"not null" json:"-" validate:"required,min=8"`
	CreatedAt  time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt  time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

type RegisterRequest struct {
	Username        string `json:"username" validate:"required,min=3,max=30,alphanum"`
	Email           string `json:"email" validate:"required,email"`
	Password        string `json:"password" validate:"required,min=8"`
	ConfirmPassword string `json:"confirmPassword" validate:"required,eqfield=Password"`
	AcceptTerms     bool   `json:"acceptTerms" validate:"required"`
}

type LoginRequest struct {
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required"`
}

type LoginAttempt struct {
	ID        uint      `gorm:"primaryKey"`
	UserID    uint      `gorm:"index"`
	Email     string    `gorm:"index"`
	IP        string    `gorm:"index"`
	Success   bool      `gorm:"index"`
	Timestamp time.Time `gorm:"index"`
}

type LoginAttempts struct {
	Count     int       `json:"count"`
	LastReset time.Time `json:"last_reset"`
}