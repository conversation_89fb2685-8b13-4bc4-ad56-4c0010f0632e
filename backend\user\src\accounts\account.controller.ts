import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Req,
  HttpCode,
  HttpStatus,
  UnauthorizedException,
} from '@nestjs/common';
import { AccountsService } from './accounts.service';
import { CreateAccountDto } from './dto/create-account.dto';
import { Request } from 'express';
import * as jwt from 'jsonwebtoken';

interface AuthRequest extends Request {
  headers: {
    authorization?: string;
  };
}

@Controller('accounts')
export class AccountsController {
  constructor(private readonly accountsService: AccountsService) {}

  // Extraire userId du token JWT
  private extractUserIdFromToken(authHeader: string | undefined): number {
    if (!authHeader) {
      throw new UnauthorizedException('Token manquant');
    }

    const token = authHeader.replace('Bearer ', '');
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET_USER || 'super_secret_key');
      return (decoded as any).user_id; // Assurez-vous que le champ correspond à votre JWT Go
    } catch {
      throw new UnauthorizedException('Token invalide');
    }
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  async create(
    @Req() req: AuthRequest,
    @Body() createAccountDto: CreateAccountDto,
  ) {
    try {
      const userId = this.extractUserIdFromToken(req.headers.authorization);
      
      const account = await this.accountsService.create(
        userId,
        createAccountDto,
      );
      
      return {
        success: true,
        message: 'Compte bancaire créé avec succès',
        account,
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      return {
        success: false,
        error: error.message || 'Erreur lors de la création du compte',
      };
    }
  }

  @Get()
  async findAll(@Req() req: AuthRequest) {
    try {
      const userId = this.extractUserIdFromToken(req.headers.authorization);
      
      const accounts = await this.accountsService.findAllByUser(userId);
      
      return {
        success: true,
        accounts,
        count: accounts.length,
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      return {
        success: false,
        error: error.message || 'Erreur lors de la récupération des comptes',
      };
    }
  }

  @Get(':id')
  async findOne(@Req() req: AuthRequest, @Param('id') id: string) {
    try {
      const userId = this.extractUserIdFromToken(req.headers.authorization);
      
      const account = await this.accountsService.findOne(
        parseInt(id),
        userId,
      );
      
      return {
        success: true,
        account,
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      return {
        success: false,
        error: error.message || 'Erreur lors de la récupération du compte',
      };
    }
  }

  @Get('references')
  async getReferences() {
    try {
      const [banks, accountTypes, currencies] = await Promise.all([
        this.accountsService.getBanks(),
        this.accountsService.getAccountTypes(),
        this.accountsService.getCurrencies(),
      ]);

      return {
        success: true,
        data: {
          banks,
          accountTypes,
          currencies,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Erreur lors de la récupération des données de référence',
      };
    }
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Req() req: AuthRequest, @Param('id') id: string) {
    try {
      const userId = this.extractUserIdFromToken(req.headers.authorization);
      
      await this.accountsService.remove(parseInt(id), userId);
      
      return {
        success: true,
        message: 'Compte supprimé avec succès',
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      return {
        success: false,
        error: error.message || 'Erreur lors de la suppression du compte',
      };
    }
  }
}