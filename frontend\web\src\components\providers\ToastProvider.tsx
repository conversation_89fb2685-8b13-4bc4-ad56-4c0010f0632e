'use client';

import { Toaster } from 'react-hot-toast';
import { toastConfig } from '../lib/toast';

export default function ToastProvider() {
  return (
    <Toaster
      position={toastConfig.position}
      toastOptions={{
        duration: toastConfig.duration,
        style: toastConfig.style,
        success: toastConfig.success,
        error: toastConfig.error,
      }}
      containerStyle={{
        top: 20,
        left: 20,
        bottom: 20,
        right: 20,
      }}
    />
  );
}
