import { Wallet, ArrowUp } from "lucide-react";

export default function SoldeTotal() {
  return (
    <div className="bg-slate-800/40 backdrop-blur-sm rounded-xl border border-slate-700/50 p-4 flex flex-col items-start">
              <div className="flex items-center gap-2 mb-3">
                <Wallet className="w-4 h-4 text-emerald-400" />
                <span className="text-xs font-inter text-slate-400">SOLDE TOTAL</span>
              </div>
              <div className="text-xl font-bold font-satoshi text-white">24,589.50 €</div>
              <div className="flex items-center gap-1 mt-auto">
                <ArrowUp className="w-3 h-3 text-emerald-400" />
                <span className="text-xs text-emerald-400 font-inter">+12.4%</span>
              </div>
            </div>
  );
}