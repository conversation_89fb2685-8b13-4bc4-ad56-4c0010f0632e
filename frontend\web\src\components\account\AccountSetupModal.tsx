'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {  CreditCard, Wallet, Landmark, Coins, ArrowRight, X, Banknote } from 'lucide-react';
import { useAccounts } from '@/hooks/useAccounts';
import { toastSuccess, toastError } from '@/components/lib/toast';

interface AccountSetupModalProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: () => void;
}

export default function AccountSetupModal({ isOpen, onClose, onComplete }: AccountSetupModalProps) {
  const { banks, accountTypes, currencies, loading: dataLoading, createAccount } = useAccounts();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const [formData, setFormData] = useState({
    name: '',
    accountNumber: '',
    bankId: banks.length > 0 ? banks[0].id : 0,
    accountTypeId: accountTypes.length > 0 ? accountTypes[0].id : 0,
    currencyId: currencies.length > 0 ? currencies[0].id : 0,
    balance: 0,
  });

  // Mettre à jour les IDs par défaut quand les données sont chargées
  useEffect(() => {
    if (banks.length > 0 && formData.bankId === 0) {
      setFormData(prev => ({ ...prev, bankId: banks[0].id }));
    }
    if (accountTypes.length > 0 && formData.accountTypeId === 0) {
      setFormData(prev => ({ ...prev, accountTypeId: accountTypes[0].id }));
    }
    if (currencies.length > 0 && formData.currencyId === 0) {
      setFormData(prev => ({ ...prev, currencyId: currencies[0].id }));
    }
  }, [banks, accountTypes, currencies]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ 
      ...prev, 
      [name]: name === 'balance' ? parseFloat(value) || 0 : value 
    }));
  };

  const handleBankSelect = (bankId: number) => {
    setFormData(prev => ({ ...prev, bankId }));
  };

  const handleAccountTypeSelect = (accountTypeId: number) => {
    setFormData(prev => ({ ...prev, accountTypeId }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const result = await createAccount(formData);
      
      if (result.success) {
        toastSuccess('Compte bancaire ajouté avec succès!');
        onComplete();
        onClose();
      } else {
        toastError(result.error || "Erreur lors de l'ajout du compte");
      }
    } catch (error) {
      toastError("Erreur inattendue lors de l'ajout du compte");
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const nextStep = () => setCurrentStep(prev => Math.min(3, prev + 1));
  const prevStep = () => setCurrentStep(prev => Math.max(1, prev - 1));

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-gradient-to-br from-slate-800/80 to-indigo-900/60 backdrop-blur-xl rounded-2xl shadow-2xl border border-slate-700 p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto"
          >
            {/* Header */}
            <div className="flex justify-between items-center mb-6">
              <div className="flex items-center gap-3">
                <div className="bg-gradient-to-br from-blue-500 to-purple-600 w-10 h-10 rounded-full flex items-center justify-center">
                  <Banknote className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-bold font-satoshi bg-gradient-to-r from-blue-300 to-emerald-300 bg-clip-text text-transparent">
                    Configuration du compte
                  </h2>
                  <p className="text-sm text-slate-400">
                    Étape {currentStep} sur 3
                  </p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="text-slate-400 hover:text-slate-200 transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            {/* Progress bar */}
            <div className="mb-8">
              <div className="flex justify-between text-xs text-slate-400 mb-2">
                <span>Informations de base</span>
                <span>Type de compte</span>
                <span>Solde initial</span>
              </div>
              <div className="w-full bg-slate-700 rounded-full h-2">
                <motion.div
                  className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full"
                  initial={{ width: '0%' }}
                  animate={{ width: `${(currentStep / 3) * 100}%` }}
                  transition={{ duration: 0.3 }}
                />
              </div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Étape 1: Informations de base */}
              {currentStep === 1 && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="space-y-6"
                >
                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">
                      Nom du compte
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      placeholder="Ex: Compte courant principal"
                      required
                      className="w-full bg-slate-800/50 border border-slate-700 rounded-lg px-4 py-3 text-slate-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">
                      Numéro de compte (IBAN)
                    </label>
                    <input
                      type="text"
                      name="accountNumber"
                      value={formData.accountNumber}
                      onChange={handleInputChange}
                      placeholder="FR76 XXXX XXXX XXXX XXXX XXXX XXX"
                      className="w-full bg-slate-800/50 border border-slate-700 rounded-lg px-4 py-3 text-slate-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-4">
                      Sélectionnez votre banque
                    </label>
                    {dataLoading ? (
                      <div className="grid grid-cols-4 gap-3">
                        {[...Array(4)].map((_, i) => (
                          <div key={i} className="h-16 bg-slate-700 rounded-lg animate-pulse" />
                        ))}
                      </div>
                    ) : (
                      <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-3 max-h-40 overflow-y-auto">
                        {banks.map(bank => (
                          <motion.button
                            key={bank.id}
                            type="button"
                            onClick={() => handleBankSelect(bank.id)}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            className={`flex flex-col items-center p-3 rounded-lg border ${
                              formData.bankId === bank.id
                                ? 'bg-blue-600/20 border-blue-500'
                                : 'bg-slate-800/50 border-slate-700 hover:border-slate-500'
                            }`}
                          >
                            <div className="w-8 h-8 mb-1 flex items-center justify-center">
                              <img 
                                src={bank.logoUrl} 
                                alt={bank.name} 
                                className="w-6 h-6 object-contain" 
                              />
                            </div>
                            <span className="text-xs text-slate-300 text-center truncate w-full">
                              {bank.name}
                            </span>
                          </motion.button>
                        ))}
                      </div>
                    )}
                  </div>
                </motion.div>
              )}

              {/* Étape 2: Type de compte */}
              {currentStep === 2 && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="space-y-6"
                >
                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-4">
                      Type de compte
                    </label>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                      {accountTypes.map(type => (
                        <motion.button
                          key={type.id}
                          type="button"
                          onClick={() => handleAccountTypeSelect(type.id)}
                          whileHover={{ scale: 1.03 }}
                          className={`flex items-center gap-3 p-4 rounded-lg ${
                            formData.accountTypeId === type.id
                              ? 'bg-blue-600/20 border border-blue-500'
                              : 'bg-slate-800/50 border border-slate-700'
                          }`}
                        >
                          {type.id === 1 && <CreditCard className="w-5 h-5 text-blue-400" />}
                          {type.id === 2 && <Coins className="w-5 h-5 text-yellow-400" />}
                          {type.id === 3 && <Landmark className="w-5 h-5 text-green-400" />}
                          {type.id === 4 && <Banknote className="w-5 h-5 text-purple-400" />}
                          <span className="text-slate-300 font-inter">{type.name}</span>
                        </motion.button>
                      ))}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">
                      Devise
                    </label>
                    <select
                      name="currencyId"
                      value={formData.currencyId}
                      onChange={handleInputChange}
                      className="w-full bg-slate-800/50 border border-slate-700 rounded-lg px-4 py-3 text-slate-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      {currencies.map(currency => (
                        <option key={currency.id} value={currency.id}>
                          {currency.name} ({currency.symbol})
                        </option>
                      ))}
                    </select>
                  </div>
                </motion.div>
              )}

              {/* Étape 3: Solde initial */}
              {currentStep === 3 && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="space-y-6"
                >
                  <div className="text-center py-8">
                    <div className="mx-auto bg-gradient-to-br from-blue-500 to-purple-600 w-16 h-16 rounded-full flex items-center justify-center mb-4">
                      <Wallet className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-lg font-semibold text-slate-200 mb-2">
                      Solde initial de votre compte
                    </h3>
                    <p className="text-slate-400 text-sm">
                      Entrez le solde actuel de votre compte pour commencer
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">
                      Solde initial
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        name="balance"
                        value={formData.balance}
                        onChange={handleInputChange}
                        placeholder="0.00"
                        step="0.01"
                        min="0"
                        className="w-full bg-slate-800/50 border border-slate-700 rounded-lg px-4 py-3 text-slate-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pl-10"
                      />
                      <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400">
                        €
                      </span>
                    </div>
                    <p className="mt-2 text-xs text-slate-500">
                      Vous pourrez modifier ce solde plus tard
                    </p>
                  </div>
                </motion.div>
              )}

              {/* Boutons de navigation */}
              <div className="flex justify-between pt-4">
                <button
                  type="button"
                  onClick={currentStep === 1 ? onClose : prevStep}
                  className="px-4 py-2 rounded-lg bg-slate-800/50 backdrop-blur-sm border border-slate-700 text-slate-300 hover:bg-slate-800 transition-colors"
                >
                  {currentStep === 1 ? 'Annuler' : 'Précédent'}
                </button>

                {currentStep < 3 ? (
                  <button
                    type="button"
                    onClick={nextStep}
                    className="px-6 py-2 rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium hover:from-blue-500 hover:to-purple-500 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Suivant
                  </button>
                ) : (
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="px-6 py-2 rounded-lg bg-gradient-to-r from-green-600 to-emerald-600 text-white font-medium hover:from-green-500 hover:to-emerald-500 transition-all duration-300 shadow-lg hover:shadow-xl disabled:opacity-50"
                  >
                    {isSubmitting ? 'Création...' : 'Terminer'}
                  </button>
                )}
              </div>
            </form>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}