// FinShark Toast Notifications
'use client';
import { toast } from 'react-hot-toast';

// Configuration des icônes avec des chaînes SVG
const icons = {
  success: '✅',
  error: '❌',
  info: 'ℹ️',
  warning: '⚠️',
  loading: '⏳'
};

// Styles de base FinShark
const getBaseStyle = (type: 'success' | 'error' | 'info' | 'warning' | 'loading') => {

  switch (type) {
    case 'success':
      return {
        background: 'rgba(16, 185, 129, 0.8)',
        border: '1px solid rgba(16, 185, 129, 0.3)',
        backdropFilter: 'blur(12px)',
      };
    case 'error':
      return {
        background: 'rgba(239, 68, 68, 0.8)',
        border: '1px solid rgba(239, 68, 68, 0.3)',
        backdropFilter: 'blur(12px)',
      };
    case 'info':
      return {
        background: 'rgba(59, 130, 246, 0.8)',
        border: '1px solid rgba(59, 130, 246, 0.3)',
        backdropFilter: 'blur(12px)',
      };
    case 'warning':
      return {
        background: 'rgba(245, 158, 11, 0.8)',
        border: '1px solid rgba(245, 158, 11, 0.3)',
        backdropFilter: 'blur(12px)',
      };
    case 'loading':
      return {
        background: 'rgba(99, 102, 241, 0.8)',
        border: '1px solid rgba(99, 102, 241, 0.3)',
        backdropFilter: 'blur(12px)',
      };
    default:
      return {
        background: 'rgba(71, 85, 105, 0.8)',
        border: '1px solid rgba(71, 85, 105, 0.3)',
        backdropFilter: 'blur(12px)',
      };
  }
};

// Fonctions de toast personnalisées pour FinShark
export const toastSuccess = (message: string) => {
  return toast.success(message, {
    icon: icons.success,
    style: getBaseStyle('success'),
    duration: 4000,
    className: 'font-satoshi',
  });
};

export const toastError = (message: string) => {
  return toast.error(message, {
    icon: icons.error,
    style: getBaseStyle('error'),
    duration: 5000,
    className: 'font-satoshi',
  });
};

export const toastInfo = (message: string) => {
  return toast(message, {
    icon: icons.info,
    style: getBaseStyle('info'),
    duration: 4000,
    className: 'font-satoshi',
  });
};

export const toastWarning = (message: string) => {
  return toast(message, {
    icon: icons.warning,
    style: getBaseStyle('warning'),
    duration: 4500,
    className: 'font-satoshi',
  });
};

export const toastLoading = (message: string) => {
  return toast.loading(message, {
    icon: icons.loading,
    style: getBaseStyle('loading'),
    className: 'font-satoshi',
  });
};

// Toast personnalisé pour FinShark avec logo
export const toastFinShark = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
  const sharkIcon = '🦈';

  return toast(message, {
    icon: sharkIcon,
    style: {
      ...getBaseStyle(type),
      fontWeight: '600',
    },
    duration: 4000,
    className: 'font-satoshi',
  });
};

// Toast pour les actions financières
export const toastFinancial = {
  portfolioUpdate: (message: string) => toastSuccess(`📊 ${message}`),
  tradeExecuted: (message: string) => toastSuccess(`💰 ${message}`),
  marketAlert: (message: string) => toastWarning(`📈 ${message}`),
  connectionError: (message: string) => toastError(`🔌 ${message}`),
  dataSync: (message: string) => toastInfo(`🔄 ${message}`),
};

// Configuration globale des toasts
export const toastConfig = {
  position: 'bottom-right' as const,
  duration: 4000,
  style: {
    color: '#ffffff',
    borderRadius: '12px',
    fontFamily: 'var(--font-satoshi)',
    fontSize: '16px',
    fontWeight: '500',
    maxWidth: '500px',
    padding: '12px 16px',
    width: '400px',
    height: '4rem',
  },
  success: {
    iconTheme: {
      primary: '#10b981',
      secondary: '#ffffff',
    },
  },
  error: {
    iconTheme: {
      primary: '#ef4444',
      secondary: '#ffffff',
    },
  },
};