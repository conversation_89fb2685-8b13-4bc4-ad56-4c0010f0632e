@import "tailwindcss";

/* Fonts locales */
@font-face {
  font-family: 'Satoshi';
  src: url('/fonts/Satoshi-Variable/Satoshi-Variable.ttf') format('truetype');
  font-weight: 300 900;
  font-style: normal;
  font-display: swap;
}


/* Configuration Tailwind CSS v4 avec @theme */
@theme {
  --font-family-raleway: var(--font-raleway), ui-sans-serif, system-ui, sans-serif;
  --font-family-inter-tight: var(--font-inter-tight), ui-sans-serif, system-ui, sans-serif;
  --font-family-satoshi: var(--font-satoshi), ui-sans-serif, system-ui, sans-serif;

  --font-family-inter: var(--font-inter), ui-sans-serif, system-ui, sans-serif;
  --font-family-geist: var(--font-geist-sans), ui-sans-serif, system-ui, sans-serif;
  --font-family-grotesk: var(--font-space-grotesk), ui-sans-serif, system-ui, sans-serif;

  --font-family-geist-mono: var(--font-geist-mono), ui-monospace, monospace;
  --font-family-jetbrains: var(--font-jetbrains), ui-monospace, monospace;
  --font-family-ibm: var(--font-ibm), ui-monospace, monospace;

  --color-primary: #0a192f;
  --color-secondary: #f59e0b;
  --color-accent: #10b981;
  --color-danger: #dc2626;
  --color-muted: #71717a;
  --color-surface: #1e293b;

  /* Couleurs supplémentaires pour le design */
  --color-blue-50: #eff6ff;
  --color-blue-500: #3b82f6;
  --color-blue-600: #2563eb;
  --color-blue-700: #1d4ed8;
  --color-blue-800: #0f52ba;
  --color-blue-900: #0b3a8b;
  --color-indigo-50: #e5e7eb;
  --color-indigo-100: #e0e7ff;
  --color-purple-500: #8b5cf6;
  --color-green-500: #10b981;
  --color-text: #f8f8f8;
}



body {
  @apply bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 text-text;
}

/* Classes utilitaires pour les fonts avec Tailwind v4 */
@layer utilities {
  /* Fonts pour les titres */
  .font-raleway { font-family: var(--font-raleway), ui-sans-serif, system-ui, sans-serif; }
  .font-interTight { font-family: var(--font-inter-tight), ui-sans-serif, system-ui, sans-serif; }
  .font-satoshi { font-family: var(--font-satoshi), ui-sans-serif, system-ui, sans-serif; }

  /* Fonts pour le texte */
  .font-inter { font-family: var(--font-inter), ui-sans-serif, system-ui, sans-serif; }
  .font-geist { font-family: var(--font-geist-sans), ui-sans-serif, system-ui, sans-serif; }
  .font-grotesk { font-family: var(--font-space-grotesk), ui-sans-serif, system-ui, sans-serif; }

  /* Fonts monospace */
  .font-geistMono { font-family: var(--font-geist-mono), ui-monospace, monospace; }
  .font-jetbrains { font-family: var(--font-jetbrains), ui-monospace, monospace; }
  .font-ibm { font-family: var(--font-ibm), ui-monospace, monospace; }
}


.animate-pulse-slow {
  animation: pulse 1s infinite;
}
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.2);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}