// app/(protected)/layout.tsx
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import { getSession } from '@/components/lib/server/auth';

type ProtectedLayoutProps = {
  children: React.ReactNode;
};

export default async function ProtectedLayout({ children } : ProtectedLayoutProps) {
  const cookieStore = await cookies();
  console.log('cookies reçus :', cookieStore.getAll());
  console.log('token :', cookieStore.get('token')?.value);

  const user = await getSession();
  console.log('user from getSession :', user);

  if (!user) {
    console.log('➜ redirige vers /login');
    redirect('/login');
  }

  return <>{children}</>;
}