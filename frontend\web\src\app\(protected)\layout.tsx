// frontend/web/src/app/(protected)/layout.tsx
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import axios from 'axios';
import type { ReactNode } from 'react';

export default async function ProtectedLayout({ children }: { children: ReactNode }) {
  const cookie = await cookies();
  const token = cookie.get('token')?.value;

  if (!token) redirect('/login');

  try {
    const baseurl = process.env.NEXT_PUBLIC_API_URL_GO;
    await axios.get(`${baseurl}/api/auth/session`, {
      headers: {
        Cookie: `token=${token}`,
      },
      proxy: false,
    });
    return <>{children}</>;
  } catch (error) {
    console.error('Session invalide :', error);
    redirect('/login');
  }
}
