// frontend/web/src/app/(protected)/layout.tsx
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import axios from 'axios';
import type { ReactNode } from 'react';

export default async function ProtectedLayout({ children }: { children: ReactNode }) {
  const cookie = await cookies();
  const token = cookie.get('token')?.value;

  if (!token) redirect('/login');

  try {
    // Utilise l'URL absolue pour les composants serveur
    const baseUrl = process.env.NODE_ENV === 'production'
      ? 'https://your-domain.com'
      : 'http://localhost:3000';

    await axios.get(`${baseUrl}/api/auth/session`, {
      headers: {
        Cookie: `token=${token}`,
      },
      timeout: 5000,
    });
    return <>{children}</>;
  } catch (error) {
    console.error('Session invalide :', error);
    redirect('/login');
  }
}
