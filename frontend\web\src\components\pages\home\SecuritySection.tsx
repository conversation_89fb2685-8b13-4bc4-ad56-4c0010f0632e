import { Shield<PERSON>he<PERSON>, Lock } from "lucide-react";

const securityFeatures = [
  "Chiffrement AES-256 de bout en bout",
  "Authentification biométrique et à deux facteurs",
  "Protection contre les fraudes en temps réel",
  "Sauvegardes distribuées sur blockchain"
];


export default function SecuitySection() {
  return (
    <section className="relative z-10 max-w-7xl mx-auto px-6 py-20">
    <div className="bg-gradient-to-br from-slate-800/40 to-blue-900/20 backdrop-blur-2xl rounded-3xl border border-slate-700/50 p-8 md:p-12 overflow-hidden">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        <div>
          <div className="inline-flex items-center gap-2 px-4 py-1.5 bg-slate-800/50 rounded-full mb-6">
            <ShieldCheck className="w-5 h-5 text-emerald-400" />
            <span className="font-inter text-emerald-400">Sécurité militaire</span>
          </div>
          
          <h2 className="text-3xl md:text-4xl font-bold font-satoshi mb-6">
            Votre argent est protégé par une <span className="bg-gradient-to-r from-blue-400 to-emerald-400 bg-clip-text text-transparent">forteresse numérique</span>
          </h2>
          
          <p className="text-slate-400 font-inter mb-8 max-w-xl">
            Chez FinShark, nous utilisons un chiffrement de niveau bancaire et des protocoles de sécurité avancés pour protéger vos données et vos actifs.
          </p>
          
          <div className="space-y-4">
            {securityFeatures.map((item, index) => (
              <div key={index} className="flex items-start gap-4">
                <div className="mt-1 w-6 h-6 rounded-full bg-emerald-500/10 flex items-center justify-center">
                  <Lock className="w-3 h-3 text-emerald-400" />
                </div>
                <p className="font-inter text-slate-300">{item}</p>
              </div>
            ))}
          </div>
        </div>
        
        <div className="flex justify-center">
          <div className="relative w-full max-w-md aspect-square">
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-64 h-64 rounded-full border-4 border-slate-700/50 animate-pulse-slow"></div>
            </div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-48 h-48 rounded-full border-4 border-blue-500/30 animate-pulse-slow animation-delay-1000"></div>
            </div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-32 h-32 rounded-full border-4 border-emerald-400/30 animate-pulse-slow animation-delay-2000"></div>
            </div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-20 h-20 rounded-full bg-gradient-to-br from-blue-600/20 to-purple-600/20 flex items-center justify-center">
                <Lock className="w-8 h-8 text-white" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  );
}