{"name": "finshark", "version": "0.0.1", "description": "FinShark - Financial application with NestJS backend and Next.js frontend", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"dev": "concurrently \"npm run dev:api\" \"npm run dev:front\" \"npm run dev:auth\"", "dev:auth": "cd backend/auth-go && go run main.go", "dev:api": "cd backend/user && npx nest start --watch", "dev:front": "dotenv -e .env.local -- sh -c 'cd frontend/web && npx next dev -p 3000'", "build": "npm run build:api && npm run build:front", "build:api": "cd backend/user && npx nest build", "build:front": "cd frontend/web && npx next build", "start:api": "cd backend/user && node dist/main", "start:front": "cd frontend/web && npx next start", "format": "prettier --write \"backend/user/**/*.ts\" \"frontend/web/**/*.{ts,tsx,js,jsx}\" \"test/**/*.ts\"", "lint:front": "cd frontend/web && npx next lint", "lint:api": "cd backend/user && npx nest lint", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "cd backend/user && jest --config ./test/jest-e2e.json", "install:frontend": "cd frontend/web && npm install", "clean": "rm -rf node_modules backend/user/dist frontend/web/.next", "prisma:generate": "cd backend/user && npx prisma generate", "prisma:reset": "cd backend/user && npx prisma migrate reset", "prisma:migrate": "cd backend/user && npx prisma migrate dev", "prisma:studio": "cd backend/user && npx prisma studio", "prisma:pull": "cd backend/user && npx prisma db pull", "prisma:seed": "cd backend/user && npx prisma db seed"}, "prisma": {"seed": "ts-node prisma/seed"}, "dependencies": {"@nestjs/common": "^11.0.1", "@nestjs/core": "^11.0.1", "@nestjs/platform-express": "^11.0.1", "axios": "^1.11.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "geist": "^1.4.2", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "next": "15.4.5", "react": "19.1.0", "react-dom": "19.1.0", "react-hot-toast": "^2.5.2", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@prisma/client": "^6.13.0", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@tailwindcss/postcss": "^4", "@types/bcrypt": "^6.0.0", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/supertest": "^6.0.2", "bcrypt": "^6.0.0", "concurrently": "^9.2.0", "dotenv": "^17.2.1", "dotenv-cli": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.4.5", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "framer-motion": "^12.23.12", "globals": "^16.0.0", "jest": "^29.7.0", "lucide-react": "^0.535.0", "prettier": "^3.4.2", "prisma": "^6.13.0", "react-icons": "^5.5.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "tailwindcss": "^4", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "backend/user/src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../../../coverage", "testEnvironment": "node"}}