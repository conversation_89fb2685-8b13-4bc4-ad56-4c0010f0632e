'use client';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import axios from 'axios';
import { motion } from 'framer-motion';
import Input from '../../ui/Input';
import Button from '../../ui/Button';
import { BsEnvelope, BsBoxArrowInRight } from 'react-icons/bs';
import { TbLock } from 'react-icons/tb';
import { loginSchema } from '@/components/validations/loginSchema';
import { toastError, toastSuccess } from '@/components/lib/toast';

export default function FormLogin() {
  const router = useRouter();
  const [errorsValidation, setErrorsValidation] = useState<{ [key: string]: string }>({});
  const [formState, setFormState] = useState({
    email: '',
    password: '',
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormState((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    const { error } = loginSchema.validate(formState, { abortEarly: false });
    if (error) {
      const fieldErrors: Record<string, string> = {};
      error.details.forEach((err:any) => {
        fieldErrors[err.path[0]] = err.message;
      });
      setErrorsValidation(fieldErrors);
      toastError(error.details[0].message);
      return;
    }

    try {
      const { data } = await axios.post('/api/login', formState);
      toastSuccess(data.message || 'Connexion réussie');
      router.push('/mon-espace');
    } catch (err: any) {
      toastError(err.response?.data?.message || 'Erreur de connexion');
    }
  };

  return (
    <motion.div
      className="flex items-center justify-center p-4"
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        className="w-full max-w-md bg-white backdrop-blur-xl rounded-2xl shadow-2xl border border-slate-700 p-8"
        whileHover={{ y: -4 }}
        transition={{ type: "spring", stiffness: 300 }}
      >
        <motion.div
          className="space-y-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <div className="text-center">
            <h2 className="text-3xl font-bold font-satoshi bg-gradient-to-r from-blue-300 to-emerald-300 bg-clip-text text-transparent">
              Connexion
            </h2>
            <p className="mt-2 text-slate-400 font-inter">
              Accédez à votre espace personnel FinShark
            </p>
          </div>

          <form className="space-y-6" onSubmit={handleSubmit}>
            <Input
              label="Adresse email"
              name="email"
              type="email"
              value={formState.email}
              onChange={handleInputChange}
              error={errorsValidation.email}
              placeholder="<EMAIL>"
              icon={() => <BsEnvelope className="text-blue-400" />}
              className="bg-slate-800/50 border-slate-700 focus:border-blue-500 group-hover:shadow-lg transition-shadow"
            />

            <Input
              label="Mot de passe"
              name="password"
              type="password"
              value={formState.password}
              onChange={handleInputChange}
              error={errorsValidation.password}
              placeholder="********"
              icon={() => <TbLock className="text-blue-400" />}
              className="bg-slate-800/50 border-slate-700 focus:border-blue-500 group-hover:shadow-lg transition-shadow"
            />

            <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
              <Button
                type="submit"
                className="w-full py-3 rounded-xl bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold hover:from-blue-500 hover:to-purple-500 focus:outline-none focus:ring-4 focus:ring-blue-500/50 transition-all duration-300 shadow-lg hover:shadow-xl"
                size="lg"
                icon={BsBoxArrowInRight}
              >
                Se connecter
              </Button>
            </motion.div>
          </form>

          <motion.div className="text-center" whileHover={{ scale: 1.05 }}>
            <p className="text-slate-400 font-inter">
              Pas encore de compte ?{" "}
              <a
                href="/register"
                className="text-blue-400 hover:text-blue-300 font-medium underline transition-colors"
              >
                Créer un compte
              </a>
            </p>
          </motion.div>
        </motion.div>
      </motion.div>
    </motion.div>
  );
}