# 🦈 FinShark - Gestionnaire Financier Personnel

**Monorepo unifié** - Structure de développement optimisée avec un seul package.json à la racine.

## 📁 Structure du projet

```
finShark-co/
├── backend/
│   └── user/                       # API NestJS
│       ├── src/
│       ├── test/
│       ├── tsconfig.json
│       └── nest-cli.json
├── frontend/
│   └── web/                        # Application Next.js
│       ├── app/
│       ├── components/
│       ├── public/
│       ├── package.json
│       ├── next.config.js
│       └── tsconfig.json
├── package.json                    # Package principal avec toutes les dépendances
├── package-lock.json
└── README.md
```

## 🚀 Démarrage rapide

### Prérequis
- Node.js 18+
- npm ou yarn

### Installation

1. **C<PERSON>r le projet**
```bash
git clone https://github.com/petaccia/finShark.git
cd finShark-co
```

2. **Installer toutes les dépendances**
```bash
npm install
```

3. **Installer les dépendances du frontend**
```bash
npm run install:frontend
```

4. **Démarrer les deux applications**
```bash
npm run dev
```
→ Backend disponible sur http://localhost:3001
→ Frontend disponible sur http://localhost:3000

## 🛠️ Technologies

### Backend
- **NestJS** - Framework Node.js robuste
- **TypeScript** - Typage statique
- **PostgreSQL** - Base de données (à configurer)

### Frontend  
- **Next.js 15** - Framework React avec SSR
- **TypeScript** - Typage statique
- **Tailwind CSS** - Framework CSS utilitaire
- **Lucide React** - Icônes modernes

## 📋 Fonctionnalités prévues

### Phase 1 - MVP (En cours)
- [ ] Authentification utilisateur
- [ ] Inscription/Connexion
- [ ] Dashboard de base
- [ ] Gestion des comptes

### Phase 2 - Fonctionnalités avancées
- [ ] Agrégation bancaire
- [ ] Analyses financières
- [ ] Notifications
- [ ] Coffre-fort numérique

## 🔧 Développement

### Scripts disponibles

**Développement**
```bash
npm run dev          # Démarre backend et frontend en parallèle
npm run dev:api      # Démarre uniquement le backend
npm run dev:front    # Démarre uniquement le frontend
```

**Build**
```bash
npm run build        # Build les deux applications
npm run build:api    # Build uniquement le backend
npm run build:front  # Build uniquement le frontend
```

**Production**
```bash
npm run start:api    # Démarre le backend en production
npm run start:front  # Démarre le frontend en production
```

**Maintenance**
```bash
npm run test         # Lance les tests
npm run lint         # Vérifie le code avec ESLint
npm run format       # Formate le code avec Prettier
npm run clean        # Nettoie les dossiers de build
```

### Configuration

1. **Variables d'environnement backend** (`.env`)
```env
DATABASE_URL=postgresql://user:password@localhost:5432/finshark
JWT_SECRET=your-secret-key
PORT=3001
```

2. **Variables d'environnement frontend** (`.env.local`)
```env
NEXT_PUBLIC_API_URL=http://localhost:3001
```

## 📚 Documentation

- **Cahier des charges complet** : `cahier-des-charges/Cahier_des_charges_FinShark.md`
- **Documentation backend** : `backend/README.md`
- **Documentation frontend** : `frontend/README.md`

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/nouvelle-fonctionnalite`)
3. Commit les changements (`git commit -am 'Ajouter nouvelle fonctionnalité'`)
4. Push vers la branche (`git push origin feature/nouvelle-fonctionnalite`)
5. Créer une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 👨‍💻 Auteur

**Sébastien Petaccia**
- Email: <EMAIL>
- GitHub: [@petaccia](https://github.com/petaccia)

---

🦈 **FinShark** - Votre partenaire pour une gestion financière intelligente et sécurisée.
