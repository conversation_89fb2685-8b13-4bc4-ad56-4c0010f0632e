// frontend/web/src/app/mon-espace/page.tsx
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import AccountSetupModal from '@/components/accounts/AccountSetupModal';

// 🔐 Cette page est rendue côté serveur
export default async function MonEspacePage() {
  const cookieStore = await cookies();
  const token = cookieStore.get('token')?.value;

  // 🔍 Vérifie la session via une API interne
  let user = null;
  let needsSetup = true; // Exemple: si l'utilisateur n'a pas encore configuré son compte

  try {
    // Utilise l'URL absolue pour les composants serveur
    const baseUrl = process.env.NODE_ENV === 'production'
      ? 'https://your-domain.com'
      : 'http://localhost:3000';

    const response = await fetch(`${baseUrl}/api/auth/session`, {
      headers: {
        'Cookie': `token=${token}`,
      },
      cache: 'no-store',
    });

    if (response.ok) {
      const data = await response.json();
      user = data.user || data; // Gère les deux formats de réponse
    }
  } catch (error) {
    console.error('Erreur vérification session:', error);
  }

  // 🔐 Si pas connecté → redirection
  if (!user) {
    redirect('/login');
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 to-slate-800">
      {/* Header */}
      <header className="bg-[var(--color-background)] shadow-lg py-4 px-6 flex justify-between items-center border-b border-[var(--color-primary)]">
        <div className="flex items-center">
          <div className="bg-[var(--color-primary)] rounded-xl w-10 h-10 flex items-center justify-center">
            <span className="text-[var(--color-quaternary)] font-bold text-xl">FS</span>
          </div>
          <span className="ml-3 text-xl font-bold text-[var(--color-text)]">FinShark</span>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="relative">
            <div className="bg-[var(--color-primary)] rounded-full w-8 h-8 flex items-center justify-center">
              <span className="text-[var(--color-text)] text-sm font-medium">
                {user.username?.charAt(0).toUpperCase() || 'U'}
              </span>
            </div>
          </div>
          <span className="text-sm font-medium text-[var(--color-text)]">
            {user.username}
          </span>
        </div>
      </header>

      {/* Contenu principal */}
      <main className="flex-grow p-6">
        <div className="max-w-4xl mx-auto">
          <div className="text-center py-12">
            <h1 className="text-3xl font-bold text-slate-100 mb-4">
              Bienvenue, {user.username} 👋
            </h1>
            <p className="text-lg text-slate-400 max-w-2xl mx-auto">
              Gérez vos comptes, effectuez des transactions, consultez vos analyses et configurez vos préférences.
            </p>
          </div>

          {/* Section d'informations */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            {[
              { 
                title: 'Gestion des comptes', 
                description: 'Ajoutez et gérez tous vos comptes bancaires',
                icon: '🏦'
              },
              { 
                title: 'Transactions', 
                description: 'Suivez toutes vos dépenses et revenus',
                icon: '💳'
              },
              { 
                title: 'Analyses', 
                description: 'Visualisez vos habitudes de consommation',
                icon: '📊'
              },
              { 
                title: 'Budgets', 
                description: 'Planifiez et suivez vos objectifs financiers',
                icon: '💰'
              },
              { 
                title: 'Notifications', 
                description: 'Configurez vos alertes personnalisées',
                icon: '🔔'
              },
              { 
                title: 'Support', 
                description: 'Contactez notre équipe pour toute question',
                icon: '💬'
              }
            ].map((item, index) => (
              <div 
                key={index} 
                className="bg-slate-800/50 backdrop-blur-sm rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow border border-slate-700"
              >
                <div className="text-3xl mb-3">{item.icon}</div>
                <h3 className="text-xl font-semibold text-slate-100 mb-2">{item.title}</h3>
                <p className="text-slate-400">{item.description}</p>
              </div>
            ))}
          </div>

          {/* Message de bienvenue */}
          <div className="bg-gradient-to-r from-blue-900/50 to-purple-900/50 border border-slate-700 rounded-xl p-6 text-center">
            <h2 className="text-xl font-bold text-slate-100 mb-2">
              Prêt à gérer vos finances ?
            </h2>
            <p className="text-slate-300 mb-4">
              Configurez votre premier compte pour débloquer toutes les fonctionnalités de FinShark.
            </p>
            <button
              onClick={() => (window as any).setupModal = true}
              className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-500 hover:to-purple-500 transition-all duration-300 shadow-lg hover:shadow-xl font-medium"
            >
              Configurer mon premier compte
            </button>
          </div>
        </div>
      </main>

      {/* Modale de configuration initiale */}
      <AccountSetupModal
        isOpen={needsSetup} // ou géré via un état en client component
        onClose={() => {}}
        onComplete={() => console.log('Configuration terminée')}
      />
    </div>
  );
}