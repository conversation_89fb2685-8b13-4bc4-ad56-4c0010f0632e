'use client';

import { useState } from 'react';
import AccountSetupModal from '@/components/accounts/AccountSetupModal';

export default function MonEspacePage() {
  const [showSetupModal, setShowSetupModal] = useState(true);

  const handleSetupComplete = () => {
    // Rediriger vers le dashboard ou rafraîchir les données
    console.log('Configuration terminée!');
    setShowSetupModal(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 to-slate-800">
      {/* Header */}
      <header className="bg-[var(--color-background)] shadow-lg py-4 px-6 flex justify-between items-center border-b border-[var(--color-primary)]">
        <div className="flex items-center">
          <div className="bg-[var(--color-primary)] rounded-xl w-10 h-10 flex items-center justify-center">
            <span className="text-[var(--color-quaternary)] font-bold text-xl">FS</span>
          </div>
          <span className="ml-3 text-xl font-bold text-[var(--color-text)]">FinShark</span>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="relative">
            <div className="bg-[var(--color-primary)] rounded-full w-8 h-8 flex items-center justify-center">
              <span className="text-[var(--color-text)] text-sm font-medium">U</span>
            </div>
          </div>
          <span className="text-sm font-medium text-[var(--color-text)]">
            Utilisateur
          </span>
        </div>
      </header>

      {/* Contenu principal */}
      <main className="flex-grow p-6">
        <div className="max-w-4xl mx-auto">
          <div className="text-center py-12">
            <h1 className="text-3xl font-bold text-slate-100 mb-4">
              Bienvenue dans votre espace personnel
            </h1>
            <p className="text-lg text-slate-400 max-w-2xl mx-auto">
              Gérez vos comptes, effectuez des transactions, consultez vos analyses et configurez vos préférences.
            </p>
          </div>

          {/* Section d'informations */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            {[
              { 
                title: 'Gestion des comptes', 
                description: 'Ajoutez et gérez tous vos comptes bancaires',
                icon: '🏦'
              },
              { 
                title: 'Transactions', 
                description: 'Suivez toutes vos dépenses et revenus',
                icon: '💳'
              },
              { 
                title: 'Analyses', 
                description: 'Visualisez vos habitudes de consommation',
                icon: '📊'
              },
              { 
                title: 'Budgets', 
                description: 'Planifiez et suivez vos objectifs financiers',
                icon: '💰'
              },
              { 
                title: 'Notifications', 
                description: 'Configurez vos alertes personnalisées',
                icon: '🔔'
              },
              { 
                title: 'Support', 
                description: 'Contactez notre équipe pour toute question',
                icon: '💬'
              }
            ].map((item, index) => (
              <div 
                key={index} 
                className="bg-slate-800/50 backdrop-blur-sm rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow border border-slate-700"
              >
                <div className="text-3xl mb-3">{item.icon}</div>
                <h3 className="text-xl font-semibold text-slate-100 mb-2">{item.title}</h3>
                <p className="text-slate-400">{item.description}</p>
              </div>
            ))}
          </div>

          {/* Message de bienvenue */}
          <div className="bg-gradient-to-r from-blue-900/50 to-purple-900/50 border border-slate-700 rounded-xl p-6 text-center">
            <h2 className="text-xl font-bold text-slate-100 mb-2">
              Commencez votre parcours financier
            </h2>
            <p className="text-slate-300 mb-4">
              Configurez votre premier compte pour débloquer toutes les fonctionnalités de FinShark.
            </p>
            <button
              onClick={() => setShowSetupModal(true)}
              className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-500 hover:to-purple-500 transition-all duration-300 shadow-lg hover:shadow-xl font-medium"
            >
              Configurer mon premier compte
            </button>
          </div>
        </div>
      </main>

      {/* Modale de configuration initiale */}
      <AccountSetupModal
        isOpen={showSetupModal}
        onClose={() => setShowSetupModal(false)}
        onComplete={handleSetupComplete}
      />
    </div>
  );
}