package controllers

import (
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/ton-org/finshark-auth/models"
	"github.com/ton-org/finshark-auth/services"
	"github.com/ton-org/finshark-auth/utils"
	"gorm.io/gorm"
)

func LoginHandler(db *gorm.DB) gin.HandlerFunc {
	validate := utils.GetValidator()
	
	return func(c *gin.Context) {
		clientIP := c.ClientIP()
		
		// Vérifier le rate limiting
		if isRateLimited(clientIP) {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error": "Trop de tentatives de connexion. Réessayez dans 15 minutes.",
			})
			return
		}

		var req models.LoginRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Données invalides"})
			return
		}

		// Validation avec validator/v10
		if err := validate.Struct(req); err != nil {
			var errors []map[string]string
			
			for _, err := range err.(validator.ValidationErrors) {
				errors = append(errors, map[string]string{
					"field":   err.Field(),
					"message": formatValidationErrorLogin(err),
				})
			}
			
			c.JSON(http.StatusBadRequest, gin.H{"errors": errors})
			return
		}

		// Tentative de login
		token, user, err := services.LoginUser(db, req.Email, req.Password)
		if err != nil {
			// Incrémenter le compteur d'échecs
			incrementFailedAttempts(clientIP)
			
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Identifiants invalides"})
			return
		}

		// Reset le compteur en cas de succès
		resetFailedAttempts(clientIP)

		// Définir le cookie
		http.SetCookie(c.Writer, &http.Cookie{
			Name:     "token",
			Value:    token,
			Path:     "/",
			MaxAge:   15 * 60,
			HttpOnly: true,
			Secure:   false,
			SameSite: http.SameSiteLaxMode,
		})

		c.JSON(http.StatusOK, gin.H{
			"message": "Connexion réussie",
			"user": gin.H{
				"id":         user.ID,
				"username":   user.Username,
				"email":      user.Email,
				"created_at": user.CreatedAt,
			},
		})
	}
}

// Variables pour le rate limiting
var (
	loginAttempts = make(map[string]*models.LoginAttempts)
	attemptsMutex = sync.RWMutex{}
	maxAttempts   = 5
	resetTime     = 15 * time.Minute
)

// Fonctions de rate limiting
func isRateLimited(ip string) bool {
	attemptsMutex.RLock()
	defer attemptsMutex.RUnlock()
	
	attempt, exists := loginAttempts[ip]
	if !exists {
		return false
	}
	
	// Reset si plus de 15 minutes
	if time.Since(attempt.LastReset) > resetTime {
		return false
	}
	
	return attempt.Count >= maxAttempts
}

func incrementFailedAttempts(ip string) {
	attemptsMutex.Lock()
	defer attemptsMutex.Unlock()
	
	attempt, exists := loginAttempts[ip]
	if !exists {
		loginAttempts[ip] = &models.LoginAttempts{
			Count:     1,
			LastReset: time.Now(),
		}
		return
	}
	
	// Reset si plus de 15 minutes
	if time.Since(attempt.LastReset) > resetTime {
		attempt.Count = 1
		attempt.LastReset = time.Now()
	} else {
		attempt.Count++
	}
}

func resetFailedAttempts(ip string) {
	attemptsMutex.Lock()
	defer attemptsMutex.Unlock()
	
	delete(loginAttempts, ip)
}

func formatValidationErrorLogin(err validator.FieldError) string {
	switch err.Tag() {
	case "required":
		return "Ce champ est requis"
	case "email":
		return "Le format de l'email est invalide"
	default:
		return "Validation échouée"
	}
}