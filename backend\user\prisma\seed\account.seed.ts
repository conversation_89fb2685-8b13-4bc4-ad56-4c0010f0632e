import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  // Seed des banques avec URLs statiques
  const banks = [
    { 
      name: 'BNP Paribas', 
      logoUrl: '/banks/bnp-paribas.svg' 
    },
    { 
      name: 'Société Générale', 
      logoUrl: '/banks/societe-generale.svg' 
    },
    { 
      name: 'Crédit Agricole', 
      logoUrl: '/banks/credit-agricole.svg' 
    },
    { 
      name: 'LCL', 
      logoUrl: '/banks/lcl.svg' 
    },
    { 
      name: 'La Banque Postale', 
      logoUrl: '/banks/banque-postale.svg' 
    },
    { 
      name: 'HSBC', 
      logoUrl: '/banks/hsbc.svg' 
    },
    { 
      name: 'BPCE', 
      logoUrl: '/banks/bpce.svg' 
    },
    { 
      name: 'Crédit Mutuel', 
      logoUrl: '/banks/credit-mutuel.svg' 
    },
    { 
      name: 'B-forbank', 
      logoUrl: '/banks/b-forbank.svg' 
    },
    { 
      name: 'CIC', 
      logoUrl: '/banks/cic.svg' 
    },
    { 
      name: 'Fortuneo', 
      logoUrl: '/banks/fortuneo.svg' 
    },
    { 
      name: 'Monabanq', 
      logoUrl: '/banks/monabanq.svg' 
    },
    { 
      name: 'Hello Bank!', 
      logoUrl: '/banks/HelloBank.svg' 
    },
    { 
      name: 'Bred Banque Populaire', 
      logoUrl: '/banks/banque-populaire.svg' 
    },
    { 
      name: 'Natixis', 
      logoUrl: '/banks/natixis.svg' 
    },
    { 
      name: 'Boursorama', 
      logoUrl: '/banks/boursorama.svg' 
    },
  ];

  for (const bank of banks) {
    await prisma.bank.upsert({
      where: { name: bank.name },
      update: {},
      create: bank,
    });
  }

  // Seed des types de comptes
  const accountTypes = [
    { name: 'Compte courant' },
    { name: 'Compte épargne' },
    { name: 'Compte joint' },
    { name: 'Compte professionnel' },
  ];

  for (const type of accountTypes) {
    await prisma.accountType.upsert({
      where: { name: type.name },
      update: {},
      create: type,
    });
  }

  // Seed des devises (commencer avec EUR seulement)
  const currencies = [
    { code: 'EUR', name: 'Euro', symbol: '€' },
  ];

  for (const currency of currencies) {
    await prisma.currency.upsert({
      where: { code: currency.code },
      update: {},
      create: currency,
    });
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });