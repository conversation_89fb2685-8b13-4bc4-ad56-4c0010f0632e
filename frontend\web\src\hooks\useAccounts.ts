'use client';

import { useState, useEffect } from 'react';
import axios from 'axios';

interface Bank {
  id: number;
  name: string;
  logoUrl: string;
}

interface AccountType {
  id: number;
  name: string;
}

interface Currency {
  id: number;
  code: string;
  name: string;
  symbol: string;
}

interface Account {
  id: number;
  name: string;
  accountNumber: string;
  balance: number;
  bank: Bank;
  accountType: AccountType;
  currency: Currency;
  createdAt: string;
}

interface AccountFormData {
  name: string;
  accountNumber: string;
  bankId: number;
  accountTypeId: number;
  currencyId: number;
  balance: number;
}

export function useAccounts() {
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [banks, setBanks] = useState<Bank[]>([]);
  const [accountTypes, setAccountTypes] = useState<AccountType[]>([]);
  const [currencies, setCurrencies] = useState<Currency[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Charger tous les comptes de l'utilisateur
  const fetchAccounts = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/accounts');
      
      if (response.data.success) {
        setAccounts(response.data.accounts);
      }
    } catch (err: any) {
      setError('Erreur lors du chargement des comptes');
      console.error('Error fetching accounts:', err);
    } finally {
      setLoading(false);
    }
  };

  // Charger les données de référence
  const fetchReferenceData = async () => {
    try {
      const response = await axios.get('/api/accounts/references');
      
      if (response.data.success) {
        setBanks(response.data.data.banks);
        setAccountTypes(response.data.data.accountTypes);
        setCurrencies(response.data.data.currencies);
      }
    } catch (err: any) {
      setError('Erreur lors du chargement des données de référence');
      console.error('Error fetching reference data:', err);
    }
  };

  // Créer un compte
  const createAccount = async (accountData: AccountFormData) => {
    try {
      const response = await axios.post('/api/accounts', accountData);
      
      if (response.data.success) {
        await fetchAccounts(); // Rafraîchir la liste
        return { success: true,  response: response.data.account };
      } else {
        return { success: false, error: response.data.error };
      }
    } catch (err: any) {
      return { 
        success: false, 
        error: err.response?.data?.error || 'Erreur lors de la création du compte' 
      };
    }
  };

  // Supprimer un compte
  const deleteAccount = async (id: number) => {
    try {
      const response = await axios.delete(`/api/accounts/${id}`);
      
      if (response.data.success) {
        await fetchAccounts(); // Rafraîchir la liste
        return { success: true };
      } else {
        return { success: false, error: response.data.error };
      }
    } catch (err: any) {
      return { 
        success: false, 
        error: err.response?.data?.error || 'Erreur lors de la suppression du compte' 
      };
    }
  };

  // Charger les données au démarrage
  useEffect(() => {
    Promise.all([
      fetchAccounts(),
      fetchReferenceData()
    ]).finally(() => {
      setLoading(false);
    });
  }, []);

  return {
    accounts,
    banks,
    accountTypes,
    currencies,
    loading,
    error,
    createAccount,
    deleteAccount,
    refetch: fetchAccounts
  };
}