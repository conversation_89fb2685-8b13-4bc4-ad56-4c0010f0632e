import { Goal } from "lucide-react";

export default function Objectifs() {
  return (
    <div className="bg-slate-800/40 backdrop-blur-sm rounded-xl border border-slate-700/50 p-4 flex flex-col">
              <div className="flex items-center gap-2 mb-3">
                <Goal className="w-4 h-4 text-cyan-400" />
                <span className="text-xs font-inter text-slate-400">OBJECTIFS</span>
              </div>
              <div className="flex-1 flex items-center justify-center">
                <div className="relative w-16 h-16">
                  {/* Cercle de progression */}
                  <div className="absolute inset-0 rounded-full border-4 border-slate-700"></div>
                  <div 
                    className="absolute inset-0 rounded-full  border-4 border-cyan-500 border-t-transparent border-r-transparent transform -rotate-45"
                    style={{ clipPath: 'polygon(50% 50%, 100% 50%, 100% 0)' }}
                  ></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-sm font-bold text-white">65%</span>
                  </div>
                </div>
              </div>
              <div className="text-xs font-inter text-slate-400 mt-2">Vacances: 1,250€/1,900€</div>
            </div>
  );
}