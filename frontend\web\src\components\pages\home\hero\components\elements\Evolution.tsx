import { LineChart } from "lucide-react";

export default function Evolution() {
  return (
    <div className="bg-slate-800/40 backdrop-blur-sm rounded-xl border border-slate-700/50 p-4 relative">
              <div className="absolute top-3 left-3 flex items-center gap-2">
                <LineChart className="w-4 h-4 text-blue-400" />
                <span className="text-xs font-inter text-slate-400">ÉVOLUTION</span>
              </div>
              <div className="mt-8 relative h-16">
                {/* Ligne de graphique */}
                <div className="absolute bottom-0 w-full">
                  <div className="flex items-end justify-between px-2 h-16">
                    {[20, 45, 30, 60, 40, 80, 60].map((value, index) => (
                      <div 
                        key={index}
                        className="w-2 bg-gradient-to-t from-blue-500 to-blue-400 rounded-t"
                        style={{ height: `${value}%` }}
                      ></div>
                    ))}
                  </div>
                </div>
                {/* Ligne de tendance */}
                <div className="absolute bottom-0 left-0 w-full h-px bg-slate-600"></div>
              </div>
            </div>
  );
}