import type { <PERSON><PERSON><PERSON> } from "next";
import {
  <PERSON><PERSON><PERSON>_<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Inter,
  Inter_Tight,
  Space_Grotesk,
  JetBrains_Mono,
  IBM_Plex_Mono
} from 'next/font/google';
// import { <PERSON><PERSON>st<PERSON>ans, GeistMono } from 'geist/font';
import "./globals.css";
import ToastProvider from "@/components/providers/ToastProvider";
import Footer from "@/components/layout/Footer";

// Fonts pour les titres
const raleway = Raleway({
  variable: "--font-raleway",
  subsets: ["latin"],
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
  display: "swap",
  fallback: ["sans-serif"],
});

const interTight = Inter_Tight({
  variable: "--font-inter-tight",
  subsets: ["latin"],
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
  display: "swap",
  fallback: ["sans-serif"],
});

// <PERSON><PERSON> font is loaded locally via @font-face in globals.css

// Fonts pour le texte
const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
  display: "swap",
  fallback: ["sans-serif"],
});

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
  display: "swap",
  fallback: ["sans-serif"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
  display: "swap",
  fallback: ["monospace"],
});

const spaceGrotesk = Space_Grotesk({
  variable: "--font-space-grotesk",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  display: "swap",
  fallback: ["sans-serif"],
});

// Fonts monospace
const jetBrainsMono = JetBrains_Mono({
  variable: "--font-jetbrains",
  subsets: ["latin"],
  weight: ["100","200", "300", "400", "500", "600", "700", "800"],
  display: "swap",
  fallback: ["monospace"],
});

const ibmPlexMono = IBM_Plex_Mono({
  variable: "--font-ibm",
  subsets: ["latin"],
  weight: ["100","200", "300", "400", "500", "600", "700"],
  display: "swap",
  fallback: ["monospace"],
});

export const metadata: Metadata = {
  title: "FinShark",
  description: "Financial application for stock analysis",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`
          ${raleway.variable} 
          ${interTight.variable} 
          ${inter.variable} 
          ${spaceGrotesk.variable} 
          ${jetBrainsMono.variable} 
          ${ibmPlexMono.variable}  
          ${geistSans.variable}
          ${geistMono.variable}
          antialiased font-satoshi`}
      >
        {children}
        <Footer />
        <ToastProvider />
      </body>
    </html>
  );
}
