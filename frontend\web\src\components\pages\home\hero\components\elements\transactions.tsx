import  { ListOrdered } from "lucide-react";

export default function Transactions() {
  return (
    <div className="bg-slate-800/40 backdrop-blur-sm rounded-xl border border-slate-700/50 p-4">
              <div className="flex items-center gap-2 mb-3">
                <ListOrdered className="w-4 h-4 text-pink-400" />
                <span className="text-xs font-inter text-slate-400">DERNIÈRES</span>
              </div>
              <div className="space-y-2">
                {[
                  { name: 'Supermarché', amount: '-49.99€', time: '10:45' },
                  { name: 'Amazon', amount: '-89.50€', time: 'Hier' },
                  { name: 'Salaire', amount: '+2,450.00€', time: '01 Sep' },
                ].map((item, index) => (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <div className="text-slate-300 font-inter truncate">{item.name}</div>
                    <div 
                      className={`font-medium ${item.amount.startsWith('+') ? 'text-emerald-400' : 'text-rose-400'}`}
                    >
                      {item.amount}
                    </div>
                    <div className="text-slate-500 text-xs">{item.time}</div>
                  </div>
                ))}
              </div>
            </div>
  );
}