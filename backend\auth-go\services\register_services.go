package services

import (
	"fmt"
	

	"github.com/ton-org/finshark-auth/models"
	"github.com/ton-org/finshark-auth/utils"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

func RegisterUser(db *gorm.DB, user *models.User, confirmPassword string, acceptTerms bool) (string, error) {
	// Validation des termes
	if !acceptTerms {
		return "", fmt.Errorf("vous devez accepter les conditions d'utilisation")
	}

	// Validation des mots de passe
	if user.Password != confirmPassword {
		return "", fmt.Errorf("les mots de passe ne correspondent pas")
	}

	// Validation avec validator
	validate := utils.GetValidator()
	if err := validate.Struct(user); err != nil {
		return "", fmt.Errorf("données invalides")
	}

	// Vérification de l'unicité avant insertion
	if err := checkUserUniqueness(db, user.Email, user.Username); err != nil {
		return "", err // Erreur déjà formatée
	}

	// Hash du mot de passe
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(user.Password), bcrypt.DefaultCost)
	if err != nil {
		return "", fmt.Errorf("erreur lors de la création du compte")
	}
	user.Password = string(hashedPassword)

	// Création de l'utilisateur
	if err := db.Create(user).Error; err != nil {
		// Gestion des erreurs de contrainte de manière générique
		return "", fmt.Errorf("erreur lors de la création du compte")
	}

	// Génération du JWT
	token, err := utils.GenerateJWT(user.ID, user.Email)
	if err != nil {
		return "", fmt.Errorf("erreur lors de la génération du token")
	}

	return token, nil
}

// Fonction pour vérifier l'unicité des champs
func checkUserUniqueness(db *gorm.DB, email, username string) error {
	var existingUser models.User
	
	// Vérifier l'email
	if err := db.Where("email = ?", email).First(&existingUser).Error; err == nil {
		return fmt.Errorf("cet email est déjà utilisé")
	}
	
	// Vérifier le username
	if err := db.Where("username = ?", username).First(&existingUser).Error; err == nil {
		return fmt.Errorf("ce nom d'utilisateur est déjà pris")
	}
	
	return nil
}