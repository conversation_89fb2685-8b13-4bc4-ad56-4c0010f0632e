import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

interface LoginRequest {
  email: string;
  password: string;
}

interface LoginResponse {
  message: string;
  user: {
    id: number;
    username: string;
    email: string;
    created_at: string;
  };
  token: string;
}

export async function POST(req: NextRequest) {
  try {
    const body: LoginRequest = await req.json();

    // Debug: Log des données reçues
    console.log('DEBUG Frontend - Données de connexion reçues:', {
      email: body.email,
      passwordLength: body.password ? body.password.length : 0
    });

    // Validation des données d'entrée
    if (!body.email || !body.password) {
      return NextResponse.json(
        { message: 'Email et mot de passe sont requis' },
        { status: 400 }
      );
    }

    // Validation basique du format email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(body.email)) {
      return NextResponse.json(
        { message: 'Le format de l\'email est invalide' },
        { status: 400 }
      );
    }

    const response = await axios.post<LoginResponse>(
      'http://localhost:8080/api/auth/login',
      body,
      {
        headers: { 'Content-Type': 'application/json' },
        timeout: 10000
      }
    );

    // Créer la réponse Next.js avec les cookies du backend Go
    const jsonResponse = NextResponse.json(
      {
        message: response.data.message,
        user: response.data.user
      },
      { status: 200 }
    );

    // Transférer les cookies du backend Go
    const setCookieHeader = response.headers['set-cookie'];
    if (setCookieHeader) {
      if (Array.isArray(setCookieHeader)) {
        setCookieHeader.forEach(cookie => {
          jsonResponse.headers.append('Set-Cookie', cookie);
        });
      } else {
        jsonResponse.headers.append('Set-Cookie', setCookieHeader);
      }
    }

    return jsonResponse;

  } catch (err: any) {
    console.error('Login error:', err.response?.data || err.message);

    // Gestion cohérente des erreurs pour correspondre aux attentes du frontend
    const errorMessage = err.response?.data?.error ||
                        err.response?.data?.message ||
                        'Erreur lors de la connexion';

    return NextResponse.json(
      { message: errorMessage },
      { status: err.response?.status || 500 }
    );
  }
}