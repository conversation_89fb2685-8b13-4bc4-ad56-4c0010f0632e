'use client';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { ShieldCheck, Wallet, TrendingUp } from 'lucide-react';

export default function LeftPanel() {
  const container = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { staggerChildren: 0.2, duration: 0.6 },
    },
  };

  const item = {
    hidden: { opacity: 0, x: -20 },
    visible: { opacity: 1, x: 0, transition: { duration: 0.4 } },
  };

  const features = [
    {
      icon: <ShieldCheck className="w-6 h-6 text-emerald-400" />,
      title: 'Confidentialité totale',
      desc: 'Vos données restent sur votre appareil, jamais partagées.',
    },
    {
      icon: <Wallet className="w-6 h-6 text-blue-400" />,
      title: 'Budget en temps réel',
      desc: 'Visualisez vos dépenses et économies instantanément.',
    },
    {
      icon: <TrendingUp className="w-6 h-6 text-purple-400" />,
      title: 'Planification intelligente',
      desc: 'P<PERSON>voyez vos besoins sans jamais être à découvert.',
    },
  ];

  return (
    <motion.div
      className="hidden lg:flex flex-col justify-center space-y-8 px-8"
      variants={container}
      initial="hidden"
      animate="visible"
    >
      {/* Logo */}
      <motion.div className="flex items-center gap-4" variants={item}>
        <motion.div
          className="w-20 h-20 rounded-2xl bg-gradient-to-br from-blue-500 via-indigo-600 to-purple-700 flex items-center justify-center shadow-lg"
          whileHover={{ scale: 1.05 }}
          transition={{ type: 'spring', stiffness: 300 }}
        >
          <Image 
            src="/logo/logo.png" 
            alt="Logo FinShark" 
            width={96} 
            height={96} 
            className="object-contain" 
          />
        </motion.div>
        <motion.div>
          <h1 className="text-4xl font-bold font-satoshi bg-gradient-to-r from-blue-300 to-emerald-300 bg-clip-text text-transparent">
            FinShark
          </h1>
          <p className="text-lg text-slate-400 font-inter">
            Gestion financière 100 % privée
          </p>
        </motion.div>
      </motion.div>

      <motion.div className="space-y-6" variants={item}>
        <motion.h2 
          className="text-2xl font-semibold font-satoshi text-white" 
          variants={item}
        >
          Prenez le contrôle de vos finances
        </motion.h2>

        <div className="space-y-4">
          {features.map(({ icon, title, desc }, idx) => (
            <motion.div
              key={title}
              className="flex items-start gap-4 p-4 rounded-xl bg-slate-800/50 backdrop-blur-sm border border-slate-700 shadow-lg"
              variants={item}
              whileHover={{ scale: 1.02 }}
              transition={{ type: 'spring', stiffness: 200 }}
            >
              <motion.div
                className="flex-shrink-0 w-12 h-12 rounded-lg bg-gradient-to-br from-slate-800 to-slate-900 flex items-center justify-center"
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.4 }}
              >
                {icon}
              </motion.div>
              <div>
                <h3 className="font-semibold text-white">{title}</h3>
                <p className="text-sm text-slate-400">{desc}</p>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </motion.div>
  );
}