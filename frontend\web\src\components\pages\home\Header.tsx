import Link from "next/link";
import Image from "next/image";
export default function Header() {
  return (
    <header className="relative z-10 px-6 py-8 flex justify-between items-center">
      <div className="flex items-center gap-3">
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-2 rounded-xl">
          <Image
            src="/logo/logo.png"
            alt="FinShark Logo"
            width={40}
            height={40}
            className="filter drop-shadow-lg"
          />
        </div>
        <h1 className="text-2xl font-bold font-satoshi bg-gradient-to-r from-blue-300 to-emerald-300 bg-clip-text text-transparent">
          FinShark
        </h1>
      </div>

      <nav className="hidden md:flex gap-8">
        {["Fonctionnalités", "Tarifs", "À propos", "Contact"].map((item) => (
          <button
            key={item}
            className="font-inter text-slate-300 hover:text-white transition-colors duration-300 hover:scale-105"
          >
            {item}
          </button>
        ))}
      </nav>

      <div className="flex gap-4">
        <Link
          href="/login"
          className="px-4 py-2 rounded-xl font-medium font-inter text-slate-300 hover:text-white transition-colors"
        >
          Connexion
        </Link>
        <button className="px-6 py-2.5 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl font-medium font-inter text-white hover:shadow-lg hover:shadow-blue-500/30 transition-all duration-300 transform hover:scale-[1.03]">
          Essai gratuit
        </button>
      </div>
    </header>
  );
}