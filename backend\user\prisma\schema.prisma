generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id            Int         @id @default(autoincrement())
  name          String
  accountNumber String?
  bankId        Int
  bank          Bank        @relation(fields: [bankId], references: [id])
  accountTypeId Int
  accountType   AccountType @relation(fields: [accountTypeId], references: [id])
  currencyId    Int
  currency      Currency    @relation(fields: [currencyId], references: [id])
  balance       Decimal     @default(0)
  userId        Int         // Référence à l'utilisateur Go
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  @@map("accounts")
}

model Bank {
  id        Int       @id @default(autoincrement())
  name      String    @unique
  logoUrl   String?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  accounts  Account[]

  @@map("banks")
}

model AccountType {
  id        Int       @id @default(autoincrement())
  name      String    @unique
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  accounts  Account[]

  @@map("account_types")
}

model Currency {
  id        Int       @id @default(autoincrement())
  code      String    @unique
  name      String
  symbol    String
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  accounts  Account[]

  @@map("currencies")
}