import { ComponentType, forwardRef,  } from 'react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  icon?: ComponentType<{ className?: string }>;
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ 
    variant = 'primary', 
    size = 'md', 
    loading = false, 
    icon, 
    children, 
    className = '', 
    disabled,
    ...props 
  }, ref) => {
    const baseClasses = `
      inline-flex items-center justify-center gap-2 
      font-medium rounded-xl transition-all duration-200 
      focus:outline-none focus:ring-2 focus:ring-offset-2 
      disabled:opacity-50 disabled:cursor-not-allowed
      relative overflow-hidden
    `;

    const variants = {
      primary: `
        bg-gradient-to-r from-blue-600 to-blue-700 
        hover:from-blue-700 hover:to-blue-800 
        text-white shadow-lg hover:shadow-xl 
        focus:ring-blue-500
      `,
      secondary: `
        bg-gradient-to-r from-slate-600 to-slate-700 
        hover:from-slate-700 hover:to-slate-800 
        text-white shadow-lg hover:shadow-xl 
        focus:ring-slate-500
      `,
      outline: `
        border-2 border-blue-600 text-blue-600 
        hover:bg-blue-600 hover:text-white 
        focus:ring-blue-500
      `,
      ghost: `
        text-slate-600 hover:text-slate-900 
        hover:bg-slate-100 dark:text-slate-400 
        dark:hover:text-slate-100 dark:hover:bg-slate-800
        focus:ring-slate-500
      `
    };

    const sizes = {
      sm: 'px-3 py-2 text-sm',
      md: 'px-6 py-3 text-base',
      lg: 'px-8 py-4 text-lg'
    };

    return (
      <button
        ref={ref}
        disabled={disabled || loading}
        className={`
          ${baseClasses}
          ${variants[variant]}
          ${sizes[size]}
          ${className}
        `}
        {...props}
      >
        {loading && (
          <svg
            className="animate-spin -ml-1 mr-2 h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        )}
        {!loading && icon && <span>{}</span>}
        {children}
      </button>
    );
  }
);

Button.displayName = 'Button';

export default Button;
