// utils/jwt.go
package utils

import (
	"os"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

func GenerateJWT(userID uint, email string) (string, error) {
	// 🔐 Récupère le secret depuis la variable d'environnement
	jwtSecret := os.Getenv("JWT_SECRET_AUTH")
	if jwtSecret == "" {
		return "", jwt.ErrInvalidKey 
	}

	claims := jwt.MapClaims{
		"user_id": userID,
		"email":   email,
		"exp":     time.Now().Add(15 * time.Minute).Unix(), // ⏱ Durée courte pour plus de sécurité
		"iat":     time.Now().Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// Signe le token avec le secret
	return token.SignedString([]byte(jwtSecret))
}