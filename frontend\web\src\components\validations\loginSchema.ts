import Joi from 'joi';

export const loginSchema = Joi.object({
  email: Joi.string().email({ tlds: false }).required().messages({
    'string.empty': 'L’email est requis',
    'string.email': '<PERSON><PERSON> invalide',
  }),
  password: Joi.string().min(6).required().messages({
    'string.empty': 'Le mot de passe est requis',
    'string.min': 'Le mot de passe doit contenir au moins 6 caractères',
  }),
});
