import { IsNotEmpty, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "class-validator";

export class CreateAccountDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  accountNumber: string;

  @IsOptional()
  @IsNumber()
  @Min(0)
  bankId: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  accountTypeId: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  currencyId: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  balance: number;
}