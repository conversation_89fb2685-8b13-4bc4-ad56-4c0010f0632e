'use server';

import { cookies } from 'next/headers';

export type AuthUser = {
  id: string;
  email: string;
  role: 'client' | 'admin';
};

export async function getSession(): Promise<AuthUser | null> {
  const cookieStore = await cookies();
  const token = cookieStore.get('token')?.value;
  if (!token) return null;

  try {
    const baseUrl = process.env.NEXT_PUBLIC_API_URL_PUBLIC;
    // 1) On appelle NOTRE propre route Next.js
    const res = await fetch(`${baseUrl}/api/auth/session`, {
      headers: {
        // on forward le cookie pour que ton handler Next.js le récupère
        cookie: `token=${token}`,
      },
      cache: 'no-store',
    });

    console.log('Go status', res.status);
    const body = await res.text();
    console.log('Go body', body);

    if (!res.ok) return null;

    // 2) On déballe { user } que renvoie ton handler
    const { user } = (await res.json()) as { user: AuthUser | null };
    return user;
  } catch (err) {
    console.error('error parsing / calling Go', err);
    return null;
  }
}