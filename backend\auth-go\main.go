package main

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/ton-org/finshark-auth/config"
	"github.com/ton-org/finshark-auth/models"
	"github.com/ton-org/finshark-auth/routes"
)

func main() {
	config.InitDB()

	// Debug: Vérifier les utilisateurs en base
	db := config.GetDb()
	var userCount int64
	db.Model(&models.User{}).Count(&userCount)
	fmt.Printf("DEBUG: Nombre d'utilisateurs en base: %d\n", userCount)

	// Lister quelques utilisateurs pour debug
	var users []models.User
	db.Select("id, username, email, created_at").Limit(5).Find(&users)
	fmt.Printf("DEBUG: Premiers utilisateurs:\n")
	for _, user := range users {
		fmt.Printf("  - ID: %d, Username: %s, Email: %s\n", user.ID, user.Username, user.Email)
	}

	router := gin.Default()
	routes.SetupRoutes(router)
	router.Run(":8080")
}
