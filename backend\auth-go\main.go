// backend/auth-go/main.go
package main

import (
	"fmt"
	"log"
	"os"
	"path/filepath"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	"github.com/ton-org/finshark-auth/config"
	"github.com/ton-org/finshark-auth/models"
	"github.com/ton-org/finshark-auth/routes"
)

func main() {
	// 🔽 Charger le .env depuis la racine du projet
	projectRoot := filepath.Join("..", "..") // finshark-co/
	envPath := filepath.Join(projectRoot, ".env")

	if err := godotenv.Load(envPath); err != nil {
		log.Printf("⚠️ .env non trouvé à %s, utilisation des variables système", envPath)
	}

	// Vérifie que JWT_SECRET_AUTH est bien chargé
	if os.Getenv("JWT_SECRET_AUTH") == "" {
		log.Fatal("❌ Erreur : JWT_SECRET_AUTH non défini dans .env")
	}

	config.InitDB()

	// Debug: Vérifier les utilisateurs en base
	db := config.GetDb()
	var userCount int64
	db.Model(&models.User{}).Count(&userCount)
	fmt.Printf("DEBUG: Nombre d'utilisateurs en base: %d\n", userCount)

	// Lister quelques utilisateurs pour debug
	var users []models.User
	db.Select("id, username, email, created_at").Limit(5).Find(&users)
	fmt.Printf("DEBUG: Premiers utilisateurs:\n")
	for _, user := range users {
		fmt.Printf("  - ID: %d, Username: %s, Email: %s\n", user.ID, user.Username, user.Email)
	}

	router := gin.New()
	router.SetTrustedProxies(nil)

	routes.SetupRoutes(router)

	// Utilise le PORT depuis .env
	port := os.Getenv("PORT_AUTH")
	if port == "" {
		port = "8080"
	}

	log.Printf("🚀 Serveur d'auth démarré sur http://localhost:%s", port)
	if err := router.Run(":" + port); err != nil {
		log.Fatalf("❌ Échec du démarrage : %v", err)
	}
}
