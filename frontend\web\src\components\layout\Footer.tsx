import Image from "next/image";

const footerLinks = [
  {
    title: "Produit",
    links: ["Fonctionnalités", "Tarifs", "Intégrations", "Roadmap"]
  },
  {
    title: "Ressour<PERSON>",
    links: ["Blog", "Tutor<PERSON><PERSON>", "Centre d'aide", "API"]
  },
  {
    title: "Entreprise",
    links: ["À propos", "Carrières", "Contact", "Partenaires"]
  }
];

export default function Footer() {
  return (
         
          <footer className="relative z-10 border-t border-slate-800 mt-20 py-12">
          <div className="max-w-7xl mx-auto px-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div>
                <div className="flex items-center gap-3 mb-6">
                  <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-2 rounded-xl">
                    <Image 
                      src="/logo/logo.png" 
                      alt="FinShark Logo" 
                      width={30} 
                      height={30} 
                    />
                  </div>
                  <h2 className="text-xl font-bold font-satoshi bg-gradient-to-r from-blue-300 to-emerald-300 bg-clip-text text-transparent">
                    FinShark
                  </h2>
                </div>
                <p className="text-slate-500 font-inter text-sm">
                  Révolutionnez votre gestion financière avec une intelligence artificielle dédiée.
                </p>
              </div>
              
              {footerLinks.map((section, index) => (
                <div key={index}>
                  <h3 className="font-satoshi font-bold text-slate-300 mb-4">{section.title}</h3>
                  <ul className="space-y-3">
                    {section.links.map((link, linkIndex) => (
                      <li key={linkIndex}>
                        <a 
                          href="#" 
                          className="font-inter text-slate-500 hover:text-slate-300 transition-colors text-sm"
                        >
                          {link}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
            
            <div className="border-t border-slate-800 mt-12 pt-8 text-center">
              <p className="text-slate-600 font-inter text-sm">
                © 2025 FinShark. Tous droits réservés. Conçu avec ❤️ pour votre liberté financière.
              </p>
            </div>
          </div>
        </footer>
  );
}  
